﻿using System;
using System.Linq;
using System.Data;
using GCData;
using System.Net;
using GenesysCloudUtils;
using Microsoft.Extensions.Logging;
using CSG.Common.ExtensionMethods;
using StandardUtils;


namespace GenesysAdapter
{
    public class GCUpdateOutboundDialingData
    {
        private const string CONTACT_LIST_TABLE_NAME = "odcontactlistdata";
        private readonly DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        private readonly ILogger? _logger;

        public GCUpdateOutboundDialingData(ILogger? logger)
        {
            _logger = logger;
        }

        public async Task<Boolean> UpdateGCContactListDataAsync()
        {
            Boolean Successful = false;
            string SyncType = CONTACT_LIST_TABLE_NAME;
            DateTime Start = DateTime.UtcNow;

            _logger?.LogInformation("Job:Start: Beginning {SyncType} job", SyncType);

            DBUtil.Initialize();

            // Initialize GCData for sync date tracking
            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            Utils UCAUtils = new Utils();

            // Use the new ContactListProcessor with smart error handling
            _logger?.LogInformation("Retrieving contact lists from Genesys Cloud using modern processor");
            DataTable ContactLists = await GetContactListsUsingContactListProcessor();

            // Only retrieve database data if we have contact lists to process
            DataTable DTODContactListData = null;
            if (ContactLists != null && ContactLists.Rows.Count > 0)
            {
                // Build optimized query to only fetch contact lists that are in scope
                var contactListIds = ContactLists.AsEnumerable()
                    .Select(row => $"'{row["contactlistid"]}'")
                    .Distinct()
                    .ToArray();

                if (contactListIds.Length > 0)
                {
                    string contactListFilter = string.Join(",", contactListIds);
                    string optimizedQuery = $"SELECT * FROM {CONTACT_LIST_TABLE_NAME} WHERE contactlistid IN ({contactListFilter})";

                    _logger?.LogInformation("Retrieving existing contact list data for {ContactListCount} contact lists", contactListIds.Length);
                    DTODContactListData = DBUtil.GetSQLTableData(optimizedQuery, "DTODContactListData");
                }
                else
                {
                    // Create empty table with same structure if no contact lists to process
                    DTODContactListData = DBUtil.CreateInMemTable(CONTACT_LIST_TABLE_NAME);
                }
            }

            if (ContactLists != null && ContactLists.Rows.Count > 0)
            {
                if(DBUtil.DBType==CSG.Adapter.Configuration.DatabaseType.Snowflake)
                {
                    UCAUtils.HandleSnowflakeColumnNames(ContactLists);
                }
                var diffResult = AnalyzeDataDifferences(ContactLists, DTODContactListData);

                _logger?.LogInformation(
                    "Contact list processing summary - GC: {GCCount}, DB: {DBCount}, Update: {UpdateCount}, Add: {AddCount}",
                    ContactLists.Rows.Count,
                    DTODContactListData.Rows.Count,
                    diffResult.UpdatedRowsCount,
                    diffResult.NewRowCount);

                RemoveUnchangedRows(ContactLists, diffResult.IdsToDelete);


                Successful = DBUtil.WriteDynamicSQLData(ContactLists, CONTACT_LIST_TABLE_NAME);

                if (Successful)
                {
                    Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, SyncType);
                }
            }
            else
            {
                _logger?.LogInformation("ODContactLists: No contact lists data was returned from Genesys Cloud");
                Successful = true; // Empty result is still successful
            }

            // Update sync date even if no data was processed (to advance the sync window)
            if (Successful)
            {
                Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, SyncType);
            }

            TimeSpan elapsed = DateTime.UtcNow - Start;
            _logger?.LogInformation("Job:End: Completed {SyncType} job in {Elapsed}", SyncType, elapsed);

            return Successful;
        }

        /// <summary>
        /// Modern contact list processing using ContactListProcessor with smart error handling
        /// </summary>
        private async Task<DataTable> GetContactListsUsingContactListProcessor()
        {
            // Initialize the legacy components needed for compatibility
            var gcUtils = new GenesysCloudUtils.GCUtils(_logger);
            gcUtils.Initialize();

            var jsonActions = new GenesysCloudUtils.JsonUtils(_logger);
            string baseUri = gcUtils.GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            string gcApiKey = gcUtils.GCApiKey;

            DataTable contactLists = DBUtil.CreateInMemTable(CONTACT_LIST_TABLE_NAME);
            DataTable contactListDetails = DBUtil.GetSQLTableData("select * from odcontactlistdetails", "odcontactlistdetails");

            if (contactListDetails.Rows.Count == 0)
            {
                _logger?.LogInformation("ODContactLists: No contact lists to process");
                return contactLists;
            }

            _logger?.LogInformation("ODContactLists: Processing {ContactListCount} contact lists using ContactListProcessor",
                contactListDetails.Rows.Count);

            // Use the new ContactListProcessor with optimized batch processing
            var processor = new GenesysCloudUtils.ContactListProcessor(_logger, jsonActions, gcApiKey, baseUri);
            var contactListRows = contactListDetails.Rows.Cast<DataRow>().ToList();

            // Convert DataRows to ContactListInfo objects for batch processing
            var contactListInfos = GenesysCloudUtils.ContactListProcessor.ConvertDataRowsToContactListInfo(contactListRows);

            // Use optimized batch processing instead of individual processing
            _logger?.LogInformation("Using optimized batch processing for {ContactListCount} contact lists", contactListInfos.Count);
            var batchResults = await processor.ProcessContactListsBatch(contactListInfos, contactLists);

            // Log final results using the new batch results structure
            _logger?.LogInformation("ODContactLists: Completed batch processing. Success: {SuccessCount}, Skipped: {SkippedCount}, Failed: {FailedCount}",
                batchResults.SuccessCount, batchResults.SkippedCount, batchResults.FailedCount);

            // Only fail the job if we have critical errors that indicate systemic issues
            if (batchResults.FailedCount > 0)
            {
                throw new InvalidOperationException($"Contact lists job failed due to {batchResults.FailedCount} critical errors. " +
                    "These indicate systemic issues that require investigation.");
            }

            return contactLists;
        }

        private (List<object> IdsToDelete, int UpdatedRowsCount, int NewRowCount) AnalyzeDataDifferences(
            DataTable contactLists, DataTable existingData)
        {
            List<object> idsToDelete = new List<object>();
            int updatedRowsCount = 0;
            int newRowCount = 0;

            foreach (DataRow row in contactLists.Rows)
            {
                DataRow[] matchingRows = existingData.Select($"keyid = '{row["keyid"]}'");

                if (matchingRows.Length > 0)
                {
                    bool needsUpdate = DetermineIfRowNeedsUpdate(row, matchingRows[0], contactLists.Columns);

                    if (needsUpdate)
                    {
                        updatedRowsCount++;
                    }
                    else
                    {
                        idsToDelete.Add(row["keyid"]);
                    }
                }
                else
                {
                    newRowCount++;
                }
            }

            return (idsToDelete, updatedRowsCount, newRowCount);
        }

        private bool DetermineIfRowNeedsUpdate(DataRow newRow, DataRow existingRow, DataColumnCollection columns)
        {
            foreach (DataColumn column in columns)
            {
                if (!existingRow.Table.Columns.Contains(column.ColumnName))
                {
                    return true;
                }

                var newValue = NormalizeValue(newRow[column.ColumnName]);
                var existingValue = NormalizeValue(existingRow[column.ColumnName]);

                if (!column.ColumnName.ToUpper().Equals("UPDATED") && !newValue.Equals(existingValue))
                {
                    return true;
                }
            }

            return false;
        }

        private string NormalizeValue(object value)
        {
            return value == DBNull.Value ? string.Empty : value.ToString();
        }

        private void RemoveUnchangedRows(DataTable contactLists, List<object> idsToDelete)
        {
            foreach (object idToDelete in idsToDelete)
            {
                DataRow[] rowsToDelete = contactLists.Select($"keyid = '{idToDelete}'");
                foreach (DataRow rowToDelete in rowsToDelete)
                {
                    contactLists.Rows.Remove(rowToDelete);
                }
            }
        }

    }
}
