using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using StandardUtils;

namespace GenesysCloudUtils
{
    /// <summary>
    /// Modern contact list processor with smart error handling and hybrid processing strategy:
    /// - Retry transient errors (network issues, rate limits)
    /// - Skip permanent errors (404 Not Found - contact list deleted)
    /// - Fail on critical errors (authentication, permissions, malformed responses)
    /// - Use API method for small lists (<10K contacts), CSV method for large lists
    /// </summary>
    public class ContactListProcessor
    {
        private readonly ILogger? _logger;
        private readonly JsonUtils _jsonActions;
        private readonly string _gcApiKey;
        private readonly string _baseUri;

        // Retry configuration
        private const int MAX_RETRIES = 3;
        private const int BASE_DELAY_MS = 1000;
        private const int MAX_WAIT_FOR_EXPORT_SECONDS = 90;
        private const int CSV_DOWNLOAD_TIMEOUT_MINUTES = 5;
        
        // Hybrid processing configuration
        private const int SMALL_LIST_THRESHOLD = 10000; // Use API for lists smaller than this
        private readonly Dictionary<string, int> _contactCountCache = new Dictionary<string, int>();

        public ContactListProcessor(ILogger? logger, JsonUtils jsonActions, string gcApiKey, string baseUri)
        {
            _logger = logger;
            _jsonActions = jsonActions;
            _gcApiKey = gcApiKey;
            _baseUri = baseUri;
        }

        /// <summary>
        /// Processes a single contact list with smart retry logic
        /// </summary>
        public async Task<ContactListProcessingResult> ProcessSingleContactListWithRetry(
            string contactListId, string contactListName, DataTable targetTable)
        {
            for (int attempt = 1; attempt <= MAX_RETRIES; attempt++)
            {
                try
                {
                    var result = await ProcessSingleContactList(contactListId, contactListName, targetTable);
                    
                    // Success - return immediately
                    if (result.Status == ContactListProcessingStatus.Success)
                    {
                        return result;
                    }
                    
                    // Permanent errors - don't retry
                    if (result.Status == ContactListProcessingStatus.SkippedPermanentError)
                    {
                        return result;
                    }
                    
                    // Transient errors - retry with exponential backoff
                    if (attempt < MAX_RETRIES)
                    {
                        int delayMs = BASE_DELAY_MS * (int)Math.Pow(2, attempt - 1);
                        _logger?.LogWarning("Retrying contact list {ContactListId} in {DelayMs}ms (attempt {Attempt}/{MaxRetries}): {Reason}",
                            contactListId, delayMs, attempt + 1, MAX_RETRIES, result.ErrorMessage);
                        await Task.Delay(delayMs);
                    }
                    else
                    {
                        // Max retries exceeded - treat as critical error
                        return new ContactListProcessingResult
                        {
                            Status = ContactListProcessingStatus.FailedCriticalError,
                            ErrorMessage = $"Max retries ({MAX_RETRIES}) exceeded. Last error: {result.ErrorMessage}"
                        };
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Unexpected error processing contact list {ContactListId} (attempt {Attempt}/{MaxRetries})",
                        contactListId, attempt, MAX_RETRIES);
                    
                    if (attempt >= MAX_RETRIES)
                    {
                        return new ContactListProcessingResult
                        {
                            Status = ContactListProcessingStatus.FailedCriticalError,
                            ErrorMessage = $"Unexpected error after {MAX_RETRIES} attempts: {ex.Message}"
                        };
                    }
                    
                    // Wait before retry
                    int delayMs = BASE_DELAY_MS * (int)Math.Pow(2, attempt - 1);
                    await Task.Delay(delayMs);
                }
            }

            // Should never reach here
            return new ContactListProcessingResult
            {
                Status = ContactListProcessingStatus.FailedCriticalError,
                ErrorMessage = "Unexpected end of retry loop"
            };
        }

        /// <summary>
        /// Processes a single contact list using hybrid approach based on contact count
        /// </summary>
        private async Task<ContactListProcessingResult> ProcessSingleContactList(
            string contactListId, string contactListName, DataTable targetTable)
        {
            try
            {
                // Step 1: Determine contact count and processing method
                int contactCount = await GetContactCount(contactListId);
                bool useApiMethod = contactCount < SMALL_LIST_THRESHOLD && contactCount > 0;
                
                _logger?.LogInformation("Processing contact list {ContactListId} ({ContactListName}): {ContactCount} contacts, using {Method} method",
                    contactListId, contactListName, contactCount, useApiMethod ? "API" : "CSV");

                if (useApiMethod)
                {
                    // Use direct API method for small lists
                    return await ProcessSmallContactListViaApi(contactListId, contactListName, targetTable, contactCount);
                }
                else
                {
                    // Use CSV export method for large lists or when count is unknown
                    return await ProcessLargeContactListViaCsv(contactListId, contactListName, targetTable);
                }
            }
            catch (ContactListNotFoundException)
            {
                return new ContactListProcessingResult
                {
                    Status = ContactListProcessingStatus.SkippedPermanentError,
                    ErrorMessage = "Contact list not found (404) - likely deleted"
                };
            }
            catch (ContactListPermissionException)
            {
                return new ContactListProcessingResult
                {
                    Status = ContactListProcessingStatus.FailedCriticalError,
                    ErrorMessage = "Permission denied - indicates authentication/authorization issue"
                };
            }
            catch (ContactListMalformedResponseException ex)
            {
                return new ContactListProcessingResult
                {
                    Status = ContactListProcessingStatus.FailedCriticalError,
                    ErrorMessage = $"Malformed API response: {ex.Message}"
                };
            }
            catch (Exception ex)
            {
                return new ContactListProcessingResult
                {
                    Status = ContactListProcessingStatus.SkippedTransientError,
                    ErrorMessage = $"Unexpected error: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Gets the contact count for a contact list, with caching to avoid redundant API calls
        /// </summary>
        private async Task<int> GetContactCount(string contactListId)
        {
            // Check cache first
            if (_contactCountCache.TryGetValue(contactListId, out int cachedCount))
            {
                return cachedCount;
            }

            try
            {
                // Use the contacts search endpoint to get count
                string searchUrl = $"{_baseUri}/api/v2/outbound/contactlists/{contactListId}/contacts/search";
                string requestBody = JsonConvert.SerializeObject(new
                {
                    pageSize = 1, // We only need the count, not the actual contacts
                    pageNumber = 1
                });

                string response = _jsonActions.JsonReturnString(searchUrl, _gcApiKey, requestBody);
                
                if (string.IsNullOrEmpty(response))
                {
                    _logger?.LogWarning("Empty response when getting contact count for {ContactListId}, falling back to CSV method", contactListId);
                    return -1; // Indicates unknown count, will use CSV method
                }

                // Check for API errors
                var errorCheck = CheckForApiErrors(response, contactListId);
                if (errorCheck != null)
                {
                    if (errorCheck.Status == ContactListProcessingStatus.SkippedPermanentError)
                    {
                        // Contact list doesn't exist, cache this result
                        _contactCountCache[contactListId] = 0;
                        return 0;
                    }
                    
                    _logger?.LogWarning("Error getting contact count for {ContactListId}: {Error}, falling back to CSV method", 
                        contactListId, errorCheck.ErrorMessage);
                    return -1; // Fall back to CSV method
                }

                // Parse the response to get total count
                var searchResult = JsonConvert.DeserializeObject<dynamic>(response);
                int totalCount = searchResult?.total ?? -1;
                
                // Cache the result
                _contactCountCache[contactListId] = totalCount;
                
                _logger?.LogDebug("Contact list {ContactListId} has {ContactCount} contacts", contactListId, totalCount);
                return totalCount;
            }
            catch (JsonReaderException ex)
            {
                _logger?.LogWarning("JSON parsing error getting contact count for {ContactListId}: {ErrorMessage}. This usually indicates the contact list doesn't exist or is inaccessible.", contactListId, ex.Message);
                return -1; // Fall back to CSV method
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Exception getting contact count for {ContactListId}, falling back to CSV method", contactListId);
                return -1; // Fall back to CSV method
            }
        }

        /// <summary>
        /// Processes small contact lists using direct API calls (faster for small lists)
        /// </summary>
        private async Task<ContactListProcessingResult> ProcessSmallContactListViaApi(
            string contactListId, string contactListName, DataTable targetTable, int expectedCount)
        {
            try
            {
                _logger?.LogDebug("Processing small contact list {ContactListId} via API (expected count: {ExpectedCount})",
                    contactListId, expectedCount);

                // Ensure required columns exist in target table
                EnsureRequiredColumnsExist(targetTable, contactListId);

                // For now, fall back to CSV method as full API implementation would require additional data structures
                // The unified column analysis approach is already implemented in the CSV method
                _logger?.LogInformation("Using CSV method for contact list {ContactListId} (unified column analysis)", contactListId);
                return await ProcessLargeContactListViaCsv(contactListId, contactListName, targetTable);
            }
            catch (Exception ex)
            {
                return new ContactListProcessingResult
                {
                    Status = ContactListProcessingStatus.SkippedTransientError,
                    ErrorMessage = $"API processing failed: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Processes large contact lists using CSV export method (more efficient for large datasets)
        /// </summary>
        private async Task<ContactListProcessingResult> ProcessLargeContactListViaCsv(
            string contactListId, string contactListName, DataTable targetTable)
        {
            try
            {
                // Step 1: Request contact list export
                string exportResponse = await RequestContactListExport(contactListId);
                if (string.IsNullOrEmpty(exportResponse))
                {
                    return new ContactListProcessingResult
                    {
                        Status = ContactListProcessingStatus.SkippedTransientError,
                        ErrorMessage = "Empty export response"
                    };
                }

                // Step 2: Check for API errors
                var errorCheck = CheckForApiErrors(exportResponse, contactListId);
                if (errorCheck != null) return errorCheck;

                // Step 3: Parse export response
                var contactListObj = ParseContactListResponse(exportResponse, contactListId);
                if (contactListObj == null)
                {
                    return new ContactListProcessingResult
                    {
                        Status = ContactListProcessingStatus.SkippedTransientError,
                        ErrorMessage = "Failed to parse contact list response"
                    };
                }

                // Step 4: Wait for export to be ready and get download URL
                string downloadUrl = await WaitForExportAndGetDownloadUrl(contactListId);
                if (string.IsNullOrEmpty(downloadUrl))
                {
                    return new ContactListProcessingResult
                    {
                        Status = ContactListProcessingStatus.SkippedTransientError,
                        ErrorMessage = "Export did not become available within timeout"
                    };
                }

                // Step 5: Download and process CSV
                int recordCount = await DownloadAndProcessCsv(contactListId, downloadUrl, targetTable);

                return new ContactListProcessingResult
                {
                    Status = ContactListProcessingStatus.Success,
                    RecordCount = recordCount
                };
            }
            catch (ContactListNotFoundException)
            {
                return new ContactListProcessingResult
                {
                    Status = ContactListProcessingStatus.SkippedPermanentError,
                    ErrorMessage = "Contact list not found (404) - likely deleted"
                };
            }
            catch (ContactListPermissionException)
            {
                return new ContactListProcessingResult
                {
                    Status = ContactListProcessingStatus.FailedCriticalError,
                    ErrorMessage = "Permission denied - indicates authentication/authorization issue"
                };
            }
            catch (ContactListMalformedResponseException ex)
            {
                return new ContactListProcessingResult
                {
                    Status = ContactListProcessingStatus.FailedCriticalError,
                    ErrorMessage = $"Malformed API response: {ex.Message}"
                };
            }
            catch (Exception ex)
            {
                return new ContactListProcessingResult
                {
                    Status = ContactListProcessingStatus.SkippedTransientError,
                    ErrorMessage = $"Unexpected error: {ex.Message}"
                };
            }
        }

        private async Task<string> RequestContactListExport(string contactListId)
        {
            string exportUrl = $"{_baseUri}/api/v2/outbound/contactlists/{contactListId}/export";
            return _jsonActions.JsonReturnString(exportUrl, _gcApiKey, "");
        }

        private ContactListProcessingResult? CheckForApiErrors(string response, string contactListId)
        {
            if (!GenesysErrorHandler.IsErrorResponse(response))
                return null;

            var errorResult = GenesysErrorHandler.CreateErrorResult(response);

            // Categorize errors
            switch (errorResult.ErrorCode?.ToLower())
            {
                case "not.found":
                case "contact.list.not.found":
                case "no.available.list.export.uri":
                    throw new ContactListNotFoundException($"Contact list {contactListId} not found or export not available");

                case "forbidden":
                case "unauthorized":
                    throw new ContactListPermissionException($"Permission denied for contact list {contactListId}");

                case "contact.list.export.in.progress":
                    // This is expected - the export request is being processed
                    return null;

                case "too.many.requests":
                case "rate.limit.exceeded":
                    return new ContactListProcessingResult
                    {
                        Status = ContactListProcessingStatus.SkippedTransientError,
                        ErrorMessage = $"Rate limit exceeded: {errorResult.ErrorMessage}"
                    };

                default:
                    return new ContactListProcessingResult
                    {
                        Status = ContactListProcessingStatus.SkippedTransientError,
                        ErrorMessage = $"API error: {errorResult.ErrorMessage}"
                    };
            }
        }

        private ContactListObject? ParseContactListResponse(string response, string contactListId)
        {
            try
            {
                return JsonConvert.DeserializeObject<ContactListObject>(response, new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                });
            }
            catch (JsonException ex)
            {
                throw new ContactListMalformedResponseException($"Failed to parse contact list response for {contactListId}: {ex.Message}");
            }
        }

        private async Task<string> WaitForExportAndGetDownloadUrl(string contactListId)
        {
            var timeout = TimeSpan.FromSeconds(MAX_WAIT_FOR_EXPORT_SECONDS);
            var startTime = DateTime.UtcNow;

            while (DateTime.UtcNow - startTime < timeout)
            {
                try
                {
                    string exportStatusUrl = $"{_baseUri}/api/v2/outbound/contactlists/{contactListId}/export";

                    // Use HttpClient directly to handle HTTP responses without exceptions
                    using var httpClient = new HttpClient();
                    httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _gcApiKey);

                    using var response = await httpClient.GetAsync(exportStatusUrl);
                    string responseBody = await response.Content.ReadAsStringAsync();

                    // Check for specific error conditions first
                    if (!response.IsSuccessStatusCode)
                    {
                        // Parse error response to check for specific error codes
                        if (response.StatusCode == System.Net.HttpStatusCode.NotFound && !string.IsNullOrEmpty(responseBody))
                        {
                            try
                            {
                                var errorResponse = JsonConvert.DeserializeObject<dynamic>(responseBody);
                                string errorCode = errorResponse?.code?.ToString()?.ToLower() ?? "";

                                if (errorCode == "no.available.list.export.uri")
                                {
                                    _logger?.LogInformation("Contact list {ContactListId} export not available (no.available.list.export.uri) - skipping", contactListId);
                                    throw new ContactListNotFoundException($"Contact list {contactListId} export not available");
                                }
                                else if (errorCode == "not.found" || errorCode == "contact.list.not.found")
                                {
                                    _logger?.LogInformation("Contact list {ContactListId} not found - skipping", contactListId);
                                    throw new ContactListNotFoundException($"Contact list {contactListId} not found");
                                }
                            }
                            catch (ContactListNotFoundException)
                            {
                                throw; // Re-throw our custom exceptions
                            }
                            catch (Exception ex)
                            {
                                _logger?.LogDebug(ex, "Error parsing error response for contact list {ContactListId}", contactListId);
                            }
                        }

                        // For other HTTP errors, treat as transient and retry
                        _logger?.LogDebug("HTTP {StatusCode} error for contact list {ContactListId}, will retry",
                            response.StatusCode, contactListId);
                        await Task.Delay(2500);
                        continue;
                    }

                    // Success response - check if we have a download URL
                    if (string.IsNullOrEmpty(responseBody) || responseBody.Length <= 20)
                    {
                        await Task.Delay(2500);
                        continue;
                    }

                    var contactListUrl = JsonConvert.DeserializeObject<ContactListUrl>(responseBody);
                    if (!string.IsNullOrEmpty(contactListUrl?.uri))
                    {
                        _logger?.LogDebug("Export URL ready for contact list {ContactListId}: {ExportUrl}",
                            contactListId, contactListUrl.uri);
                        return contactListUrl.uri;
                    }

                    // No URL yet, continue waiting
                    _logger?.LogDebug("Export URL not ready yet for contact list {ContactListId}, continuing to wait", contactListId);
                }
                catch (ContactListNotFoundException)
                {
                    // Permanent error - contact list doesn't exist or export not available
                    throw; // Re-throw to be handled by calling method
                }
                catch (ContactListPermissionException)
                {
                    // Permanent error - permission denied
                    throw; // Re-throw to be handled by calling method
                }
                catch (Exception ex)
                {
                    // Transient error - log and retry
                    _logger?.LogDebug(ex, "Transient error checking export status for {ContactListId}, will retry", contactListId);
                }

                await Task.Delay(2500);
            }

            // Timeout reached - treat as transient error
            _logger?.LogWarning("Timeout waiting for export URL for contact list {ContactListId} after {TimeoutSeconds} seconds",
                contactListId, MAX_WAIT_FOR_EXPORT_SECONDS);
            return string.Empty;
        }

        private async Task<int> DownloadAndProcessCsv(string contactListId, string downloadUrl, DataTable targetTable)
        {
            using var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _gcApiKey);
            httpClient.Timeout = TimeSpan.FromMinutes(CSV_DOWNLOAD_TIMEOUT_MINUTES);

            using var response = await httpClient.GetAsync(downloadUrl);
            response.EnsureSuccessStatusCode();

            using var responseStream = await response.Content.ReadAsStreamAsync();
            using var reader = new StreamReader(responseStream, Encoding.UTF8);

            return await ProcessContactListCsvStream(contactListId, reader, targetTable);
        }

        /// <summary>
        /// Processes CSV stream and adds data to target table with proper schema handling
        /// </summary>
        private async Task<int> ProcessContactListCsvStream(string contactListId, StreamReader reader, DataTable targetTable)
        {
            int recordCount = 0;

            try
            {
                string? headerLine = await reader.ReadLineAsync();
                if (string.IsNullOrEmpty(headerLine))
                {
                    _logger?.LogWarning("Empty CSV file for contact list {ContactListId}", contactListId);
                    return 0;
                }

                // Parse CSV headers
                string[] headers = ParseCsvLine(headerLine);
                if (headers.Length == 0)
                {
                    _logger?.LogWarning("No headers found in CSV for contact list {ContactListId}", contactListId);
                    return 0;
                }

                _logger?.LogDebug("Processing CSV for contact list {ContactListId} with {HeaderCount} columns",
                    contactListId, headers.Length);

                // Ensure required columns exist in target table
                EnsureRequiredColumnsExist(targetTable, contactListId);

                // Collect and process CSV data
                var tempData = await CollectCsvData(reader, headers);

                // Use unified method to add columns with optimal sizing
                AddDynamicColumnsFromData(targetTable, headers, tempData, contactListId);

                // Process the collected data into DataTable rows
                recordCount = PopulateDataTableFromCsvData(targetTable, tempData, contactListId);

                _logger?.LogDebug("Successfully processed CSV for contact list {ContactListId}: {RecordCount} records",
                    contactListId, recordCount);

                return recordCount;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing CSV stream for contact list {ContactListId}", contactListId);
                throw;
            }
        }

        /// <summary>
        /// Ensures required columns exist in the target table
        /// </summary>
        private void EnsureRequiredColumnsExist(DataTable targetTable, string contactListId)
        {
            // Required columns for odcontactlistdata table
            var requiredColumns = new Dictionary<string, Type>
            {
                { "keyid", typeof(string) },
                { "contactlistid", typeof(string) },
                { "updated", typeof(DateTime) }
            };

            foreach (var kvp in requiredColumns)
            {
                if (!targetTable.Columns.Contains(kvp.Key))
                {
                    var column = targetTable.Columns.Add(kvp.Key, kvp.Value);
                    if (kvp.Value == typeof(string))
                    {
                        column.MaxLength = kvp.Key == "keyid" ? 100 : 50; // Based on schema
                    }
                    _logger?.LogDebug("Added required column '{ColumnName}' to target table for contact list {ContactListId}",
                        kvp.Key, contactListId);
                }
            }
        }

        /// <summary>
        /// Unified method to analyze data and add dynamic columns with optimal sizing
        /// This method works for both CSV and API data by analyzing the actual data content
        /// </summary>
        private void AddDynamicColumnsFromData(DataTable targetTable, IEnumerable<string> headers,
            IEnumerable<Dictionary<string, string>> dataRows, string contactListId)
        {
            const int defaultDynamicColumnLength = 100;
            var columnMaxLengths = new Dictionary<string, int>();

            // Analyze all data to determine maximum lengths for each column
            foreach (var row in dataRows)
            {
                foreach (var kvp in row)
                {
                    string header = kvp.Key;
                    string value = kvp.Value ?? string.Empty;

                    if (!string.IsNullOrEmpty(header) && !string.IsNullOrEmpty(value))
                    {
                        int currentLength = value.Length;
                        if (!columnMaxLengths.ContainsKey(header) || columnMaxLengths[header] < currentLength)
                        {
                            columnMaxLengths[header] = currentLength;
                        }
                    }
                }
            }

            // Add columns with appropriate sizing based on data analysis
            foreach (string header in headers)
            {
                if (!string.IsNullOrEmpty(header) && !targetTable.Columns.Contains(header))
                {
                    // Determine appropriate column length based on actual data content
                    int maxDataLength = columnMaxLengths.ContainsKey(header) ? columnMaxLengths[header] : 0;
                    bool useTextType = maxDataLength > 100 || header.Length > 100;

                    if (useTextType)
                    {
                        targetTable.Columns.Add(header, typeof(string)); // TEXT field (no MaxLength set)
                        _logger?.LogDebug("Added TEXT column '{ColumnName}' to target table for contact list {ContactListId} (max data length: {MaxLength})",
                            header, contactListId, maxDataLength);
                    }
                    else
                    {
                        var column = targetTable.Columns.Add(header, typeof(string));
                        // Use a safe size that accommodates the data plus some buffer
                        int safeColumnLength = Math.Max(defaultDynamicColumnLength, Math.Min(maxDataLength + 50, 255));
                        column.MaxLength = safeColumnLength;
                        _logger?.LogDebug("Added VARCHAR({Length}) column '{ColumnName}' to target table for contact list {ContactListId} (max data length: {MaxLength})",
                            safeColumnLength, header, contactListId, maxDataLength);
                    }
                }
            }
        }

        /// <summary>
        /// Parses a CSV line handling quoted fields and commas
        /// </summary>
        private string[] ParseCsvLine(string line)
        {
            var result = new List<string>();
            bool inQuotes = false;
            var currentField = new StringBuilder();

            for (int i = 0; i < line.Length; i++)
            {
                char c = line[i];

                if (c == '"')
                {
                    if (inQuotes && i + 1 < line.Length && line[i + 1] == '"')
                    {
                        // Escaped quote
                        currentField.Append('"');
                        i++; // Skip next quote
                    }
                    else
                    {
                        // Toggle quote state
                        inQuotes = !inQuotes;
                    }
                }
                else if (c == ',' && !inQuotes)
                {
                    // Field separator
                    result.Add(currentField.ToString());
                    currentField.Clear();
                }
                else
                {
                    currentField.Append(c);
                }
            }

            // Add the last field
            result.Add(currentField.ToString());

            return result.ToArray();
        }

        private async Task<List<Dictionary<string, string>>> CollectCsvData(StreamReader reader, string[] headers)
        {
            var tempData = new List<Dictionary<string, string>>();

            string? line;
            while ((line = await reader.ReadLineAsync()) != null)
            {
                if (string.IsNullOrWhiteSpace(line)) continue;

                string[] values = ParseCsvLine(line);
                var rowData = new Dictionary<string, string>();

                for (int i = 0; i < Math.Min(headers.Length, values.Length); i++)
                {
                    string header = headers[i];
                    string value = values[i];

                    if (!string.IsNullOrEmpty(header))
                    {
                        rowData[header] = value ?? string.Empty;
                    }
                }

                tempData.Add(rowData);
            }

            return tempData;
        }

        private int PopulateDataTableFromCsvData(DataTable targetTable, List<Dictionary<string, string>> tempData, string contactListId)
        {
            int recordCount = 0;

            foreach (var rowData in tempData)
            {
                // Create new row
                DataRow newRow = targetTable.NewRow();

                // Set required fields
                string keyId = $"{contactListId}_{recordCount}_{DateTime.UtcNow.Ticks}";
                newRow["keyid"] = keyId;
                newRow["contactlistid"] = contactListId;
                newRow["updated"] = DateTime.UtcNow;

                // Set data from collected row data
                foreach (var kvp in rowData)
                {
                    if (targetTable.Columns.Contains(kvp.Key))
                    {
                        // Handle null values properly for database compatibility
                        newRow[kvp.Key] = string.IsNullOrEmpty(kvp.Value) ? DBNull.Value : kvp.Value;
                    }
                }

                targetTable.Rows.Add(newRow);
                recordCount++;
            }

            return recordCount;
        }
    }

    // Supporting classes and enums
    public enum ContactListProcessingStatus
    {
        Success,
        SkippedPermanentError,
        SkippedTransientError,
        FailedCriticalError
    }

    public class ContactListProcessingResult
    {
        public ContactListProcessingStatus Status { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public int RecordCount { get; set; }
    }

    public class ContactListProcessingResults
    {
        public int SuccessCount { get; set; }
        public int SkippedCount { get; set; }
        public int FailedCount { get; set; }
        public int CriticalErrorCount { get; set; }
    }

    // Custom exceptions for better error categorization
    public class ContactListNotFoundException : Exception
    {
        public ContactListNotFoundException(string message) : base(message) { }
    }

    public class ContactListPermissionException : Exception
    {
        public ContactListPermissionException(string message) : base(message) { }
    }

    public class ContactListMalformedResponseException : Exception
    {
        public ContactListMalformedResponseException(string message) : base(message) { }
    }

    public class ContactListObject
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class ContactListUrl
    {
        public string uri { get; set; }
        public DateTime exportTimestamp { get; set; }
    }
}
