using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using StandardUtils;

namespace GenesysCloudUtils
{
    /// <summary>
    /// Modern contact list processor with smart error handling and hybrid processing strategy:
    /// - Retry transient errors (network issues, rate limits)
    /// - Skip permanent errors (404 Not Found - contact list deleted)
    /// - Fail on critical errors (authentication, permissions, malformed responses)
    /// - Use API method for small lists (<10K contacts), CSV method for large lists
    /// </summary>
    public class ContactListProcessor
    {
        private readonly ILogger? _logger;
        private readonly JsonUtils _jsonActions;
        private readonly string _gcApiKey;
        private readonly string _baseUri;
        private readonly Dictionary<string, string> _contactListNames = new Dictionary<string, string>();

        // Retry configuration
        private const int MAX_RETRIES = 3;
        private const int BASE_DELAY_MS = 1000;
        private const int MAX_WAIT_FOR_EXPORT_SECONDS = 90;
        private const int CSV_DOWNLOAD_TIMEOUT_MINUTES = 5;

        // Hybrid processing configuration
        private const int SMALL_LIST_THRESHOLD = 10000; // Use API for lists smaller than this
        private readonly Dictionary<string, int> _contactCountCache = new Dictionary<string, int>();

        // Batch processing configuration
        private const int MAX_CONCURRENT_API_CALLS = 10;
        private const int MAX_CONCURRENT_CSV_EXPORTS = 5;
        private const int MAX_CONCURRENT_COUNT_CHECKS = 10;

        // Performance monitoring
        private readonly Dictionary<string, TimeSpan> _processingTimes = new();
        private readonly Dictionary<string, int> _recordCounts = new();
        private readonly Dictionary<string, string> _processingMethods = new();
        private readonly Dictionary<string, DateTime> _processingStartTimes = new();
        private readonly object _metricsLock = new();

        public ContactListProcessor(ILogger? logger, JsonUtils jsonActions, string gcApiKey, string baseUri)
        {
            _logger = logger;
            _jsonActions = jsonActions;
            _gcApiKey = gcApiKey;
            _baseUri = baseUri;
        }

        #region Batch Processing Data Structures

        /// <summary>
        /// Information about a contact list for batch processing
        /// </summary>
        public class ContactListInfo
        {
            public string ContactListId { get; set; } = string.Empty;
            public string ContactListName { get; set; } = string.Empty;
            public int EstimatedContactCount { get; set; } = -1;
            public DateTime LastModified { get; set; }
        }

        /// <summary>
        /// Categorized contact lists for batch processing
        /// </summary>
        public class ContactListCategory
        {
            public List<ContactListInfo> ApiProcessableLists { get; set; } = new();
            public List<ContactListInfo> CsvRequiredLists { get; set; } = new();
        }

        #endregion

        #region Performance Monitoring

        /// <summary>
        /// Records the start of processing for a contact list
        /// </summary>
        private void RecordProcessingStart(string contactListId, string method)
        {
            lock (_metricsLock)
            {
                _processingStartTimes[contactListId] = DateTime.UtcNow;
                _processingMethods[contactListId] = method;
            }
        }

        /// <summary>
        /// Records the completion of processing for a contact list
        /// </summary>
        private void RecordProcessingComplete(string contactListId, int recordCount)
        {
            lock (_metricsLock)
            {
                if (_processingStartTimes.TryGetValue(contactListId, out var startTime))
                {
                    var duration = DateTime.UtcNow - startTime;
                    _processingTimes[contactListId] = duration;
                    _recordCounts[contactListId] = recordCount;
                    _processingStartTimes.Remove(contactListId);
                }
            }
        }

        /// <summary>
        /// Generates a comprehensive performance summary
        /// </summary>
        public string GeneratePerformanceSummary()
        {
            lock (_metricsLock)
            {
                if (_processingTimes.Count == 0)
                {
                    return "No processing metrics available";
                }

                var summary = new StringBuilder();
                summary.AppendLine("📊 Contact List Processing Performance Summary");
                summary.AppendLine("=" + new string('=', 50));

                // Overall statistics
                var totalRecords = _recordCounts.Values.Sum();
                var totalTime = TimeSpan.FromTicks(_processingTimes.Values.Sum(t => t.Ticks));
                var avgTimePerList = TimeSpan.FromTicks((long)_processingTimes.Values.Average(t => t.Ticks));

                summary.AppendLine($"📈 Overall Statistics:");
                summary.AppendLine($"   • Total Lists Processed: {_processingTimes.Count:N0}");
                summary.AppendLine($"   • Total Records: {totalRecords:N0}");
                summary.AppendLine($"   • Total Processing Time: {totalTime:hh\\:mm\\:ss}");
                summary.AppendLine($"   • Average Time per List: {avgTimePerList:mm\\:ss}");
                summary.AppendLine($"   • Records per Second: {(totalRecords / totalTime.TotalSeconds):F1}");
                summary.AppendLine();

                // Method breakdown
                var methodGroups = _processingMethods.GroupBy(kvp => kvp.Value);
                summary.AppendLine($"🔧 Processing Method Breakdown:");
                foreach (var group in methodGroups)
                {
                    var methodLists = group.Select(g => g.Key).ToList();
                    var methodRecords = methodLists.Sum(id => _recordCounts.GetValueOrDefault(id, 0));
                    var methodTime = TimeSpan.FromTicks(methodLists.Sum(id => _processingTimes.GetValueOrDefault(id, TimeSpan.Zero).Ticks));
                    var avgMethodTime = methodLists.Count > 0 ? TimeSpan.FromTicks((long)methodLists.Average(id => _processingTimes.GetValueOrDefault(id, TimeSpan.Zero).Ticks)) : TimeSpan.Zero;

                    summary.AppendLine($"   • {group.Key}:");
                    summary.AppendLine($"     - Lists: {methodLists.Count:N0}");
                    summary.AppendLine($"     - Records: {methodRecords:N0}");
                    summary.AppendLine($"     - Total Time: {methodTime:hh\\:mm\\:ss}");
                    summary.AppendLine($"     - Avg Time per List: {avgMethodTime:mm\\:ss}");
                    if (methodTime.TotalSeconds > 0)
                        summary.AppendLine($"     - Records per Second: {(methodRecords / methodTime.TotalSeconds):F1}");
                }
                summary.AppendLine();

                // Top performers and slowest
                var sortedByTime = _processingTimes.OrderByDescending(kvp => kvp.Value).ToList();
                var sortedByRecords = _recordCounts.OrderByDescending(kvp => kvp.Value).ToList();

                summary.AppendLine($"⚡ Top 5 Fastest Lists (by time):");
                foreach (var item in sortedByTime.TakeLast(5).Reverse())
                {
                    var name = _contactListNames.GetValueOrDefault(item.Key, "Unknown");
                    var records = _recordCounts.GetValueOrDefault(item.Key, 0);
                    var method = _processingMethods.GetValueOrDefault(item.Key, "Unknown");
                    summary.AppendLine($"   • {name}: {item.Value:mm\\:ss} ({records:N0} records, {method})");
                }
                summary.AppendLine();

                summary.AppendLine($"🐌 Top 5 Slowest Lists (by time):");
                foreach (var item in sortedByTime.Take(5))
                {
                    var name = _contactListNames.GetValueOrDefault(item.Key, "Unknown");
                    var records = _recordCounts.GetValueOrDefault(item.Key, 0);
                    var method = _processingMethods.GetValueOrDefault(item.Key, "Unknown");
                    summary.AppendLine($"   • {name}: {item.Value:mm\\:ss} ({records:N0} records, {method})");
                }
                summary.AppendLine();

                summary.AppendLine($"📊 Top 5 Largest Lists (by record count):");
                foreach (var item in sortedByRecords.Take(5))
                {
                    var name = _contactListNames.GetValueOrDefault(item.Key, "Unknown");
                    var time = _processingTimes.GetValueOrDefault(item.Key, TimeSpan.Zero);
                    var method = _processingMethods.GetValueOrDefault(item.Key, "Unknown");
                    summary.AppendLine($"   • {name}: {item.Value:N0} records ({time:mm\\:ss}, {method})");
                }

                return summary.ToString();
            }
        }

        #endregion

        /// <summary>
        /// Processes a single contact list with smart retry logic
        /// </summary>
        public async Task<ContactListProcessingResult> ProcessSingleContactListWithRetry(
            string contactListId, string contactListName, DataTable targetTable)
        {
            for (int attempt = 1; attempt <= MAX_RETRIES; attempt++)
            {
                try
                {
                    var result = await ProcessSingleContactList(contactListId, contactListName, targetTable);
                    
                    // Success - return immediately
                    if (result.Status == ContactListProcessingStatus.Success)
                    {
                        return result;
                    }
                    
                    // Permanent errors - don't retry
                    if (result.Status == ContactListProcessingStatus.SkippedPermanentError)
                    {
                        return result;
                    }
                    
                    // Transient errors - retry with exponential backoff
                    if (attempt < MAX_RETRIES)
                    {
                        int delayMs = BASE_DELAY_MS * (int)Math.Pow(2, attempt - 1);
                        _logger?.LogWarning("Retrying contact list {ContactListId} in {DelayMs}ms (attempt {Attempt}/{MaxRetries}): {Reason}",
                            contactListId, delayMs, attempt + 1, MAX_RETRIES, result.ErrorMessage);
                        await Task.Delay(delayMs);
                    }
                    else
                    {
                        // Max retries exceeded - treat as critical error
                        return new ContactListProcessingResult
                        {
                            Status = ContactListProcessingStatus.FailedCriticalError,
                            ErrorMessage = $"Max retries ({MAX_RETRIES}) exceeded. Last error: {result.ErrorMessage}"
                        };
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Unexpected error processing contact list {ContactListId} (attempt {Attempt}/{MaxRetries})",
                        contactListId, attempt, MAX_RETRIES);
                    
                    if (attempt >= MAX_RETRIES)
                    {
                        return new ContactListProcessingResult
                        {
                            Status = ContactListProcessingStatus.FailedCriticalError,
                            ErrorMessage = $"Unexpected error after {MAX_RETRIES} attempts: {ex.Message}"
                        };
                    }
                    
                    // Wait before retry
                    int delayMs = BASE_DELAY_MS * (int)Math.Pow(2, attempt - 1);
                    await Task.Delay(delayMs);
                }
            }

            // Should never reach here
            return new ContactListProcessingResult
            {
                Status = ContactListProcessingStatus.FailedCriticalError,
                ErrorMessage = "Unexpected end of retry loop"
            };
        }

        #region Batch Processing Methods

        /// <summary>
        /// Processes multiple contact lists in batch using optimized parallel processing
        /// </summary>
        /// <param name="contactLists">List of contact lists to process</param>
        /// <param name="targetTable">Target DataTable to populate</param>
        /// <returns>Combined processing results</returns>
        public async Task<ContactListProcessingResults> ProcessContactListsBatch(
            List<ContactListInfo> contactLists,
            DataTable targetTable)
        {
            if (contactLists == null || contactLists.Count == 0)
            {
                _logger?.LogInformation("No contact lists to process in batch");
                return new ContactListProcessingResults();
            }

            _logger?.LogInformation("Starting batch processing of {ContactListCount} contact lists", contactLists.Count);
            var startTime = DateTime.UtcNow;

            try
            {
                // Step 0: Populate contact list names for better logging
                foreach (var list in contactLists)
                {
                    _contactListNames[list.ContactListId] = list.ContactListName;
                }

                // Step 1: Categorize contact lists based on size and processing method
                var categorized = await CategorizeContactLists(contactLists);

                // Count different types of skipped lists
                int totalProcessed = categorized.ApiProcessableLists.Count + categorized.CsvRequiredLists.Count;
                int totalSkipped = contactLists.Count - totalProcessed;

                // Count non-existent contact lists (those with -999 marker)
                int nonExistentCount = contactLists.Count(cl => cl.EstimatedContactCount == -999);
                int zeroContactsCount = totalSkipped - nonExistentCount;

                _logger?.LogInformation("Categorized contact lists: {ApiCount} for API processing, {CsvCount} for CSV processing, {ZeroContactsCount} skipped (0 contacts), {NonExistentCount} not found in Genesys Cloud",
                    categorized.ApiProcessableLists.Count, categorized.CsvRequiredLists.Count, zeroContactsCount, nonExistentCount);

                // Step 2: Start CSV exports first to give them time to process
                var csvExportTask = InitiateCsvExports(categorized.CsvRequiredLists);

                // Step 3: Process API lists in parallel while CSV exports are being prepared
                var apiResults = await ProcessApiListsInParallel(categorized.ApiProcessableLists, targetTable);

                // Step 4: Wait for CSV exports to complete and process them
                var csvResults = await ProcessCsvExportsAfterCompletion(csvExportTask, targetTable);

                // Step 5: Combine results (including skipped lists)
                var combinedResults = CombineResults(apiResults, csvResults, zeroContactsCount, nonExistentCount);

                var duration = DateTime.UtcNow - startTime;
                _logger?.LogInformation("Batch processing completed in {Duration:mm\\:ss}: {SuccessCount} successful, {SkippedCount} skipped, {FailedCount} failed",
                    duration, combinedResults.SuccessCount, combinedResults.SkippedCount, combinedResults.FailedCount);

                // Generate and log performance summary
                var performanceSummary = GeneratePerformanceSummary();
                _logger?.LogInformation("\n{PerformanceSummary}", performanceSummary);

                return combinedResults;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error during batch processing of contact lists");
                throw;
            }
        }

        /// <summary>
        /// Categorizes contact lists into API-processable vs CSV-required based on contact count
        /// </summary>
        private async Task<ContactListCategory> CategorizeContactLists(List<ContactListInfo> contactLists)
        {
            var category = new ContactListCategory();
            var semaphore = new SemaphoreSlim(MAX_CONCURRENT_COUNT_CHECKS);
            var results = new ConcurrentBag<(ContactListInfo List, int Count)>();

            _logger?.LogDebug("Categorizing {ContactListCount} contact lists based on size", contactLists.Count);

            var tasks = contactLists.Select(async list =>
            {
                await semaphore.WaitAsync();
                try
                {
                    _logger?.LogDebug("🔍 Getting contact count for {ContactListId} ({ContactListName}) using search endpoint",
                        list.ContactListId, list.ContactListName);

                    int count = await GetContactCount(list.ContactListId);

                    _logger?.LogInformation("📊 Contact list {ContactListId} ({ContactListName}): {ContactCount} contacts",
                        list.ContactListId, list.ContactListName, count);

                    results.Add((list, count));
                }
                catch (ContactListNotFoundException ex)
                {
                    _logger?.LogInformation("Contact list {ContactListId} ({ContactListName}) not found in Genesys Cloud - likely deleted",
                        list.ContactListId, list.ContactListName);
                    results.Add((list, -999)); // Special marker for non-existent contact lists
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "⚠️ Error getting contact count for {ContactListId} ({ContactListName}), defaulting to CSV processing",
                        list.ContactListId, list.ContactListName);

                    // Output additional debug information for troubleshooting
                    _logger?.LogWarning("📋 Contact count API failure details for {ContactListId}: Exception type: {ExceptionType}, Message: {ExceptionMessage}",
                        list.ContactListId, ex.GetType().Name, ex.Message);

                    results.Add((list, -1)); // Default to CSV processing
                }
                finally
                {
                    semaphore.Release();
                }
            });

            await Task.WhenAll(tasks);

            // Categorize based on results
            foreach (var (list, count) in results)
            {
                list.EstimatedContactCount = count;

                if (count == -999)
                {
                    // Contact list doesn't exist in Genesys Cloud - mark for removal
                    _logger?.LogInformation("Contact list {ContactListId} ({ContactListName}): Not found in Genesys Cloud → Marked for removal",
                        list.ContactListId, list.ContactListName);
                    // Don't add to either category - this will be handled as a permanent skip
                }
                else if (count == 0)
                {
                    // Skip lists with 0 contacts - no API calls needed
                    _logger?.LogInformation("Contact list {ContactListId} ({ContactListName}): {ContactCount} contacts → Skipped (no contacts)",
                        list.ContactListId, list.ContactListName, count);
                    // Don't add to either category - will be handled as successful with 0 records
                }
                else if (count > 0 && count < SMALL_LIST_THRESHOLD)
                {
                    category.ApiProcessableLists.Add(list);
                    _logger?.LogInformation("Contact list {ContactListId} ({ContactListName}): {ContactCount} contacts → API processing",
                        list.ContactListId, list.ContactListName, count);
                }
                else
                {
                    category.CsvRequiredLists.Add(list);
                    string countDisplay = count == -1 ? "unknown" : count.ToString();
                    string reason = count == -1 ? "unknown size" : $"large size ({count} ≥ {SMALL_LIST_THRESHOLD})";
                    _logger?.LogInformation("Contact list {ContactListId} ({ContactListName}): {ContactCount} contacts → CSV processing ({Reason})",
                        list.ContactListId, list.ContactListName, countDisplay, reason);
                }
            }

            return category;
        }

        /// <summary>
        /// Processes API-suitable contact lists in parallel
        /// </summary>
        private async Task<List<ContactListProcessingResult>> ProcessApiListsInParallel(
            List<ContactListInfo> apiLists,
            DataTable targetTable)
        {
            if (apiLists.Count == 0)
            {
                _logger?.LogDebug("No API-processable contact lists to process");
                return new List<ContactListProcessingResult>();
            }

            _logger?.LogInformation("Processing {ApiListCount} contact lists via API in parallel", apiLists.Count);

            var semaphore = new SemaphoreSlim(MAX_CONCURRENT_API_CALLS);
            var results = new ConcurrentBag<ContactListProcessingResult>();

            var tasks = apiLists.Select(async list =>
            {
                await semaphore.WaitAsync();
                try
                {
                    var result = await ProcessSmallContactListViaApi(list.ContactListId, list.ContactListName, targetTable, list.EstimatedContactCount);
                    results.Add(result);

                    if (result.Status == ContactListProcessingStatus.Success)
                    {
                        _logger?.LogInformation("✓ API processing completed for {ContactListId} ({ContactListName}): {RecordCount} records",
                            list.ContactListId, list.ContactListName, result.RecordCount);
                    }
                    else
                    {
                        _logger?.LogWarning("✗ API processing failed for {ContactListId} ({ContactListName}): {Status} - {ErrorMessage}",
                            list.ContactListId, list.ContactListName, result.Status, result.ErrorMessage);
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Error processing contact list {ContactListId} via API", list.ContactListId);
                    results.Add(new ContactListProcessingResult
                    {
                        Status = ContactListProcessingStatus.FailedCriticalError,
                        ErrorMessage = $"Unexpected error during API processing: {ex.Message}"
                    });
                }
                finally
                {
                    semaphore.Release();
                }
            });

            await Task.WhenAll(tasks);
            return results.ToList();
        }

        /// <summary>
        /// Processes CSV-required contact lists in parallel
        /// </summary>
        private async Task<List<ContactListProcessingResult>> ProcessCsvListsInParallel(
            List<ContactListInfo> csvLists,
            DataTable targetTable)
        {
            if (csvLists.Count == 0)
            {
                _logger?.LogDebug("No CSV-required contact lists to process");
                return new List<ContactListProcessingResult>();
            }

            _logger?.LogInformation("Processing {CsvListCount} contact lists via CSV in parallel", csvLists.Count);

            // Step 1: Initiate all CSV exports concurrently (with limited concurrency)
            var exportSemaphore = new SemaphoreSlim(MAX_CONCURRENT_CSV_EXPORTS);
            var exportResults = new ConcurrentBag<(ContactListInfo List, string ExportResponse, Exception? Error)>();

            var exportTasks = csvLists.Select(async list =>
            {
                await exportSemaphore.WaitAsync();
                try
                {
                    var exportResponse = await RequestContactListExport(list.ContactListId);
                    exportResults.Add((list, exportResponse, null));

                    _logger?.LogDebug("Initiated CSV export for contact list {ContactListId}", list.ContactListId);
                }
                catch (Exception ex)
                {
                    // Debug: Log the exact exception message to understand the format
                    _logger?.LogInformation("🔍 DEBUG: Exception message for contact list {ContactListId}: '{ExceptionMessage}'", list.ContactListId, ex.Message);

                    // Check if this is an "export already in progress" error
                    if (ex.Message.Contains("Export already in progress") || ex.Message.Contains("contact.list.export.in.progress"))
                    {
                        _logger?.LogInformation("Export already in progress for contact list {ContactListId}, checking for existing export URI", list.ContactListId);
                    }

                    _logger?.LogWarning(ex, "Failed to initiate CSV export for contact list {ContactListId}", list.ContactListId);
                    exportResults.Add((list, string.Empty, ex));
                }
                finally
                {
                    exportSemaphore.Release();
                }
            });

            await Task.WhenAll(exportTasks);

            // Step 2: Wait for exports to complete and download
            var downloadTasks = exportResults
                .Where(r => !string.IsNullOrEmpty(r.ExportResponse) && r.Error == null)
                .Select(async r =>
                {
                    try
                    {
                        var result = await ProcessLargeContactListViaCsv(r.List.ContactListId, r.List.ContactListName, targetTable);

                        if (result.Status == ContactListProcessingStatus.Success)
                        {
                            _logger?.LogInformation("✓ CSV processing completed for {ContactListId} ({ContactListName}): {RecordCount} records",
                                r.List.ContactListId, r.List.ContactListName, result.RecordCount);
                        }
                        else
                        {
                            _logger?.LogWarning("✗ CSV processing failed for {ContactListId} ({ContactListName}): {Status} - {ErrorMessage}",
                                r.List.ContactListId, r.List.ContactListName, result.Status, result.ErrorMessage);
                        }

                        return result;
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "Error processing contact list {ContactListId} via CSV", r.List.ContactListId);
                        return new ContactListProcessingResult
                        {
                            Status = ContactListProcessingStatus.FailedCriticalError,
                            ErrorMessage = $"Unexpected error during CSV processing: {ex.Message}"
                        };
                    }
                });

            var downloadResults = await Task.WhenAll(downloadTasks);

            // Step 3: Handle failed exports
            var failedExports = exportResults
                .Where(r => string.IsNullOrEmpty(r.ExportResponse) || r.Error != null)
                .Select(r => new ContactListProcessingResult
                {
                    Status = ContactListProcessingStatus.SkippedTransientError,
                    ErrorMessage = r.Error?.Message ?? "Failed to initiate export"
                });

            return downloadResults.Concat(failedExports).ToList();
        }

        /// <summary>
        /// Initiates CSV exports for large contact lists and returns a task to track them
        /// </summary>
        private async Task<List<(ContactListInfo List, Task<string> ExportTask)>> InitiateCsvExports(List<ContactListInfo> csvLists)
        {
            if (csvLists.Count == 0)
            {
                _logger?.LogDebug("No CSV exports to initiate");
                return new List<(ContactListInfo, Task<string>)>();
            }

            _logger?.LogInformation("🚀 Initiating {CsvCount} CSV exports in parallel", csvLists.Count);

            var exportSemaphore = new SemaphoreSlim(MAX_CONCURRENT_CSV_EXPORTS);
            var exportTasks = csvLists.Select(list => (
                List: list,
                ExportTask: Task.Run(async () =>
                {
                    await exportSemaphore.WaitAsync();
                    try
                    {
                        return await RequestContactListExportSimplified(list.ContactListId);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogWarning(ex, "Failed to initiate CSV export for contact list {ContactListId}", list.ContactListId);
                        return string.Empty;
                    }
                    finally
                    {
                        exportSemaphore.Release();
                    }
                })
            )).ToList();

            return exportTasks;
        }

        /// <summary>
        /// Processes CSV exports after they have been initiated and API processing is complete
        /// </summary>
        private async Task<List<ContactListProcessingResult>> ProcessCsvExportsAfterCompletion(
            Task<List<(ContactListInfo List, Task<string> ExportTask)>> csvExportTask,
            DataTable targetTable)
        {
            var exportTasks = await csvExportTask;

            if (exportTasks.Count == 0)
            {
                _logger?.LogDebug("No CSV exports to process");
                return new List<ContactListProcessingResult>();
            }

            _logger?.LogInformation("📥 Processing {CsvCount} CSV exports after API completion", exportTasks.Count);

            var results = new List<ContactListProcessingResult>();

            foreach (var (list, exportTask) in exportTasks)
            {
                try
                {
                    string exportResponse = await exportTask;

                    if (string.IsNullOrEmpty(exportResponse))
                    {
                        results.Add(new ContactListProcessingResult
                        {
                            Status = ContactListProcessingStatus.SkippedTransientError,
                            ErrorMessage = "Failed to initiate export"
                        });
                        continue;
                    }

                    var result = await ProcessLargeContactListViaCsv(list.ContactListId, list.ContactListName, targetTable);
                    results.Add(result);

                    if (result.Status == ContactListProcessingStatus.Success)
                    {
                        _logger?.LogInformation("✓ CSV processing completed for {ContactListId} ({ContactListName}): {RecordCount} records",
                            list.ContactListId, list.ContactListName, result.RecordCount);
                    }
                    else
                    {
                        _logger?.LogWarning("✗ CSV processing failed for {ContactListId} ({ContactListName}): {Status} - {ErrorMessage}",
                            list.ContactListId, list.ContactListName, result.Status, result.ErrorMessage);
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Error processing CSV export for contact list {ContactListId}", list.ContactListId);
                    results.Add(new ContactListProcessingResult
                    {
                        Status = ContactListProcessingStatus.FailedCriticalError,
                        ErrorMessage = $"Unexpected error during CSV processing: {ex.Message}"
                    });
                }
            }

            return results;
        }

        /// <summary>
        /// Combines results from API and CSV processing, including skipped lists
        /// </summary>
        private ContactListProcessingResults CombineResults(
            List<ContactListProcessingResult> apiResults,
            List<ContactListProcessingResult> csvResults,
            int skippedCount = 0,
            int nonExistentCount = 0)
        {
            var combined = new ContactListProcessingResults();
            var allResults = apiResults.Concat(csvResults);

            foreach (var result in allResults)
            {
                switch (result.Status)
                {
                    case ContactListProcessingStatus.Success:
                        combined.SuccessCount++;
                        break;
                    case ContactListProcessingStatus.SkippedPermanentError:
                    case ContactListProcessingStatus.SkippedTransientError:
                        combined.SkippedCount++;
                        break;
                    case ContactListProcessingStatus.FailedCriticalError:
                        combined.FailedCount++;
                        break;
                }
            }

            // Add skipped lists (0 contacts) to success count
            combined.SuccessCount += skippedCount;

            // Add non-existent lists to skipped count (they are permanently skipped)
            combined.SkippedCount += nonExistentCount;

            return combined;
        }

        /// <summary>
        /// Converts DataRow collection to ContactListInfo objects for batch processing
        /// </summary>
        public static List<ContactListInfo> ConvertDataRowsToContactListInfo(IEnumerable<DataRow> contactListRows)
        {
            return contactListRows.Select(row => new ContactListInfo
            {
                ContactListId = row["id"]?.ToString() ?? string.Empty,
                ContactListName = row["name"]?.ToString() ?? string.Empty,
                LastModified = row["datemodified"] is DateTime dt ? dt : DateTime.MinValue
            }).ToList();
        }

        #endregion

        /// <summary>
        /// Processes a single contact list using hybrid approach based on contact count
        /// </summary>
        private async Task<ContactListProcessingResult> ProcessSingleContactList(
            string contactListId, string contactListName, DataTable targetTable)
        {
            try
            {
                // Step 1: Determine contact count and processing method
                int contactCount = await GetContactCount(contactListId);
                bool useApiMethod = contactCount < SMALL_LIST_THRESHOLD && contactCount > 0;
                
                _logger?.LogInformation("Processing contact list {ContactListId} ({ContactListName}): {ContactCount} contacts, using {Method} method",
                    contactListId, contactListName, contactCount, useApiMethod ? "API" : "CSV");

                if (useApiMethod)
                {
                    // Use direct API method for small lists
                    return await ProcessSmallContactListViaApi(contactListId, contactListName, targetTable, contactCount);
                }
                else
                {
                    // Use CSV export method for large lists or when count is unknown
                    return await ProcessLargeContactListViaCsv(contactListId, contactListName, targetTable);
                }
            }
            catch (ContactListNotFoundException)
            {
                return new ContactListProcessingResult
                {
                    Status = ContactListProcessingStatus.SkippedPermanentError,
                    ErrorMessage = "Contact list not found (404) - likely deleted"
                };
            }
            catch (ContactListPermissionException)
            {
                return new ContactListProcessingResult
                {
                    Status = ContactListProcessingStatus.FailedCriticalError,
                    ErrorMessage = "Permission denied - indicates authentication/authorization issue"
                };
            }
            catch (ContactListMalformedResponseException ex)
            {
                return new ContactListProcessingResult
                {
                    Status = ContactListProcessingStatus.FailedCriticalError,
                    ErrorMessage = $"Malformed API response: {ex.Message}"
                };
            }
            catch (Exception ex)
            {
                return new ContactListProcessingResult
                {
                    Status = ContactListProcessingStatus.SkippedTransientError,
                    ErrorMessage = $"Unexpected error: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Gets the contact count for a contact list, with caching to avoid redundant API calls
        /// </summary>
        private async Task<int> GetContactCount(string contactListId)
        {
            // Check cache first
            if (_contactCountCache.TryGetValue(contactListId, out int cachedCount))
            {
                return cachedCount;
            }

            try
            {
                // Use the contacts search endpoint to get count
                string searchUrl = $"{_baseUri}/api/v2/outbound/contactlists/{contactListId}/contacts/search";
                string requestBody = JsonConvert.SerializeObject(new
                {
                    pageSize = 1, // We only need the count, not the actual contacts
                    pageNumber = 1
                });

                string response = _jsonActions.JsonReturnString(searchUrl, _gcApiKey, requestBody);
                
                if (string.IsNullOrEmpty(response))
                {
                    _logger?.LogWarning("Empty response when getting contact count for {ContactListId}, falling back to CSV method", contactListId);
                    return -1; // Indicates unknown count, will use CSV method
                }

                // Check for API errors
                var errorCheck = CheckForApiErrors(response, contactListId);
                if (errorCheck != null)
                {
                    if (errorCheck.Status == ContactListProcessingStatus.SkippedPermanentError)
                    {
                        // Contact list doesn't exist - this will be caught by CheckForApiErrors and throw ContactListNotFoundException
                        // We should never reach this point, but if we do, throw the exception
                        throw new ContactListNotFoundException($"Contact list {contactListId} not found");
                    }

                    _logger?.LogWarning("Error getting contact count for {ContactListId}: {Error}, falling back to CSV method",
                        contactListId, errorCheck.ErrorMessage);

                    // Output the JSON response for debugging
                    _logger?.LogWarning("📋 JSON Response for contact count failure on {ContactListId}: {JsonResponse}",
                        contactListId, response);

                    return -1; // Fall back to CSV method
                }

                // Parse the response to get total count with better error handling
                dynamic searchResult;
                try
                {
                    searchResult = JsonConvert.DeserializeObject<dynamic>(response);
                }
                catch (JsonException ex)
                {
                    _logger?.LogWarning(ex, "JSON parsing error getting contact count for {ContactListId}. Response may be HTML error page instead of JSON. Response preview: {ResponsePreview}",
                        contactListId, response.Length > 100 ? response.Substring(0, 100) + "..." : response);

                    // Output the full JSON response for debugging
                    _logger?.LogWarning("📋 Full JSON Response for parsing error on {ContactListId}: {JsonResponse}",
                        contactListId, response);

                    return -1; // Fall back to CSV method
                }

                int totalCount = searchResult?.total ?? -1;
                
                // Cache the result
                _contactCountCache[contactListId] = totalCount;
                
                _logger?.LogDebug("Contact list {ContactListId} has {ContactCount} contacts", contactListId, totalCount);
                return totalCount;
            }
            catch (JsonReaderException ex)
            {
                _logger?.LogWarning("JSON parsing error getting contact count for {ContactListId}: {ErrorMessage}. This usually indicates the contact list doesn't exist or is inaccessible.", contactListId, ex.Message);
                return -1; // Fall back to CSV method
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Exception getting contact count for {ContactListId}, falling back to CSV method", contactListId);

                // Output additional debug information for troubleshooting
                _logger?.LogWarning("📋 Exception details for contact count failure on {ContactListId}: Type: {ExceptionType}, Message: {ExceptionMessage}",
                    contactListId, ex.GetType().Name, ex.Message);

                return -1; // Fall back to CSV method
            }
        }

        /// <summary>
        /// Processes small contact lists using direct API calls (faster for small lists)
        /// </summary>
        private async Task<ContactListProcessingResult> ProcessSmallContactListViaApi(
            string contactListId, string contactListName, DataTable targetTable, int expectedCount)
        {
            // Start performance monitoring
            RecordProcessingStart(contactListId, "API");

            try
            {
                _logger?.LogDebug("Processing small contact list {ContactListId} via API (expected count: {ExpectedCount})",
                    contactListId, expectedCount);

                // Ensure required columns exist in target table
                EnsureRequiredColumnsExist(targetTable, contactListId);

                // Use the bulk contacts API endpoint for direct processing
                int recordCount = await ProcessContactsViaApi(contactListId, targetTable);

                // Record successful completion
                RecordProcessingComplete(contactListId, recordCount);

                return new ContactListProcessingResult
                {
                    Status = ContactListProcessingStatus.Success,
                    RecordCount = recordCount
                };
            }
            catch (ContactListNotFoundException)
            {
                return new ContactListProcessingResult
                {
                    Status = ContactListProcessingStatus.SkippedPermanentError,
                    ErrorMessage = "Contact list not found (404) - likely deleted"
                };
            }
            catch (ContactListPermissionException)
            {
                return new ContactListProcessingResult
                {
                    Status = ContactListProcessingStatus.FailedCriticalError,
                    ErrorMessage = "Permission denied - indicates authentication/authorization issue"
                };
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "API processing failed for contact list {ContactListId}, falling back to CSV method", contactListId);
                // Fall back to CSV method if API processing fails
                return await ProcessLargeContactListViaCsv(contactListId, contactListName, targetTable);
            }
        }

        /// <summary>
        /// Processes contacts using the search API endpoint
        /// </summary>
        private async Task<int> ProcessContactsViaApi(string contactListId, DataTable targetTable)
        {
            int totalRecords = 0;
            int pageNumber = 1;
            const int pageSize = 100; // Reasonable page size for API calls
            bool hasMorePages = true;

            // Get the contact list name for better logging
            string contactListName = GetContactListNameById(contactListId);
            string listDisplayName = !string.IsNullOrEmpty(contactListName) ? $"{contactListName} ({contactListId})" : contactListId;

            // Get expected contact count and calculate expected pages
            int expectedContactCount = GetContactCountFromCache(contactListId);
            int expectedPages = expectedContactCount > 0 ? (int)Math.Ceiling((double)expectedContactCount / pageSize) : 0;

            var startTime = DateTime.UtcNow;
            _logger?.LogInformation("🔍 Starting API processing for contact list {ListDisplayName}: {ExpectedContacts} contacts, ~{ExpectedPages} pages (page size: {PageSize})",
                listDisplayName, expectedContactCount, expectedPages, pageSize);

            while (hasMorePages)
            {
                try
                {
                    string searchUrl = $"{_baseUri}/api/v2/outbound/contactlists/{contactListId}/contacts/search";
                    string requestBody = JsonConvert.SerializeObject(new
                    {
                        pageSize = pageSize,
                        pageNumber = pageNumber
                    });

                    string response = _jsonActions.JsonReturnString(searchUrl, _gcApiKey, requestBody);

                    if (string.IsNullOrEmpty(response))
                    {
                        _logger?.LogWarning("Empty response from search API for contact list {ContactListId} page {PageNumber}",
                            contactListId, pageNumber);
                        break;
                    }

                    // Check for API errors
                    var errorCheck = CheckForApiErrors(response, contactListId);
                    if (errorCheck != null)
                    {
                        _logger?.LogError("❌ API error detected: {Status} - {ErrorMessage}", errorCheck.Status, errorCheck.ErrorMessage);
                        if (errorCheck.Status == ContactListProcessingStatus.SkippedPermanentError)
                        {
                            throw new ContactListNotFoundException($"Contact list {contactListId} not found");
                        }
                        throw new Exception($"API error: {errorCheck.ErrorMessage}");
                    }

                    // Parse the response with better error handling
                    dynamic contactsResponse;
                    try
                    {
                        contactsResponse = JsonConvert.DeserializeObject<dynamic>(response);
                    }
                    catch (JsonException ex)
                    {
                        _logger?.LogWarning(ex, "JSON parsing error for contact list {ContactListId} page {PageNumber}. Response may be HTML error page instead of JSON. Response preview: {ResponsePreview}",
                            contactListId, pageNumber, response.Length > 100 ? response.Substring(0, 100) + "..." : response);
                        throw new Exception($"Invalid JSON response from API: {ex.Message}");
                    }

                    var contacts = contactsResponse?.entities;

                    if (contacts == null || !contacts.HasValues)
                    {
                        break;
                    }

                    // Process contacts and add to target table
                    int pageRecords = ProcessContactsFromApiResponse(contacts, contactListId, targetTable);
                    totalRecords += pageRecords;

                    // Check if we have more pages
                    int? totalPages = contactsResponse?.pageCount;
                    hasMorePages = totalPages == null || pageNumber < totalPages;

                    pageNumber++;
                }
                catch (ContactListNotFoundException)
                {
                    _logger?.LogError("🚫 Contact list {ContactListId} not found", contactListId);
                    throw; // Re-throw permanent errors
                }
                catch (ContactListPermissionException)
                {
                    _logger?.LogError("🔒 Permission denied for contact list {ContactListId}", contactListId);
                    throw; // Re-throw permanent errors
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "💥 Error processing page {PageNumber} for contact list {ContactListId}", pageNumber, contactListId);
                    throw; // Let the calling method handle fallback to CSV
                }
            }

            var duration = DateTime.UtcNow - startTime;
            _logger?.LogInformation("✅ Completed API processing for {ListDisplayName}: {TotalRecords} contacts processed across {PageCount} pages in {Duration:mm\\:ss}",
                listDisplayName, totalRecords, pageNumber - 1, duration);

            return totalRecords;
        }

        /// <summary>
        /// Gets the contact list name by ID from the cached contact list names
        /// </summary>
        private string GetContactListNameById(string contactListId)
        {
            return _contactListNames.TryGetValue(contactListId, out string? name) ? name : string.Empty;
        }

        /// <summary>
        /// Gets the contact count from the cached contact counts
        /// </summary>
        private int GetContactCountFromCache(string contactListId)
        {
            return _contactCountCache.TryGetValue(contactListId, out int count) ? count : 0;
        }



        /// <summary>
        /// Processes contacts from API response and adds them to the target table
        /// Uses centralized column management for consistency
        /// </summary>
        private int ProcessContactsFromApiResponse(dynamic contacts, string contactListId, DataTable targetTable)
        {
            int recordCount = 0;
            int contactsWithoutData = 0;
            int contactsWithErrors = 0;
            int duplicateContacts = 0;
            var now = DateTime.UtcNow;

            // Ensure required columns exist using centralized manager
            ContactListColumnManager.EnsureRequiredColumnsExist(targetTable);

            // Create a HashSet to efficiently track processed contact IDs in this batch
            var processedContactIds = new HashSet<string>();

            // Also check existing contacts in the target table to avoid duplicates from previous batches
            var existingContactIds = ContactListColumnManager.GetExistingKeyIds(targetTable);

            // First pass: collect all contact data for column analysis
            var contactDataList = new List<Dictionary<string, string>>();
            var contactHeaders = new HashSet<string>();

            foreach (var contact in contacts)
            {
                try
                {
                    string contactId = contact?.id?.ToString();

                    // Generate keyid using centralized logic
                    string keyId = ContactListColumnManager.GenerateKeyId(contactId, contactListId, ContactDataSource.Api);

                    // Check for duplicates efficiently using HashSet
                    if (existingContactIds.Contains(keyId) || !processedContactIds.Add(keyId))
                    {
                        duplicateContacts++;
                        continue; // Skip duplicate contact
                    }

                    var contactData = new Dictionary<string, string>();

                    // Add dynamic contact data
                    if (contact?.data != null)
                    {
                        foreach (var property in contact.data)
                        {
                            string columnName = property.Name;
                            string columnValue = property.Value?.ToString() ?? string.Empty;

                            if (!string.IsNullOrEmpty(columnName))
                            {
                                contactData[columnName] = columnValue;
                                contactHeaders.Add(columnName);
                            }
                        }
                    }
                    else
                    {
                        contactsWithoutData++;
                    }

                    // Store keyid for row creation
                    contactData["_keyId"] = keyId;
                    contactDataList.Add(contactData);
                }
                catch (Exception ex)
                {
                    contactsWithErrors++;
                    if (contactsWithErrors <= 3) // Only log first few errors
                    {
                        _logger?.LogWarning(ex, "❌ Error analyzing contact from API response for contact list {ContactListId}", contactListId);
                    }
                }
            }

            // Add dynamic columns using centralized manager
            if (contactHeaders.Count > 0)
            {
                ContactListColumnManager.AddDynamicColumnsFromData(targetTable, contactHeaders, contactDataList);
            }

            // Second pass: create rows with consistent column handling
            foreach (var contactData in contactDataList)
            {
                try
                {
                    string keyId = contactData["_keyId"];
                    contactData.Remove("_keyId"); // Remove internal field

                    // Create row with required fields using centralized manager (keyId already generated)
                    var row = ContactListColumnManager.CreateRowWithRequiredFields(targetTable, contactListId, keyId, now, ContactDataSource.Api);

                    // Populate dynamic fields using centralized manager
                    ContactListColumnManager.PopulateDynamicFields(row, contactData);

                    targetTable.Rows.Add(row);
                    recordCount++;
                }
                catch (ConstraintException ex) when (ex.Message.Contains("is constrained to be unique"))
                {
                    // Handle duplicate key constraint as fallback (shouldn't happen with our HashSet check)
                    duplicateContacts++;
                    if (duplicateContacts <= 3) // Only log first few duplicates
                    {
                        _logger?.LogDebug("🔄 Duplicate contact detected for contact list {ContactListId}: {ErrorMessage}", contactListId, ex.Message);
                    }
                    // Continue processing other contacts
                }
                catch (Exception ex)
                {
                    contactsWithErrors++;
                    // Log errors but don't flood the logs - summary will show error count
                    if (contactsWithErrors <= 3) // Only log first few errors
                    {
                        _logger?.LogWarning(ex, "❌ Error creating row for contact in list {ContactListId}", contactListId);
                    }
                    // Continue processing other contacts
                }
            }

            // Log summary if there were duplicates
            if (duplicateContacts > 0)
            {
                _logger?.LogDebug("📋 Contact list {ContactListId}: Skipped {DuplicateCount} duplicate contacts", contactListId, duplicateContacts);
            }

            return recordCount;
        }

        /// <summary>
        /// Processes large contact lists using CSV export method (more efficient for large datasets)
        /// </summary>
        private async Task<ContactListProcessingResult> ProcessLargeContactListViaCsv(
            string contactListId, string contactListName, DataTable targetTable)
        {
            // Start performance monitoring
            RecordProcessingStart(contactListId, "CSV");

            try
            {
                // Step 1: Request contact list export
                string exportResponse = await RequestContactListExport(contactListId);
                if (string.IsNullOrEmpty(exportResponse))
                {
                    return new ContactListProcessingResult
                    {
                        Status = ContactListProcessingStatus.SkippedTransientError,
                        ErrorMessage = "Empty export response"
                    };
                }

                // Step 2: Check for API errors
                var errorCheck = CheckForApiErrors(exportResponse, contactListId);
                if (errorCheck != null) return errorCheck;

                // Step 3: Parse export response
                var contactListObj = ParseContactListResponse(exportResponse, contactListId);
                if (contactListObj == null)
                {
                    return new ContactListProcessingResult
                    {
                        Status = ContactListProcessingStatus.SkippedTransientError,
                        ErrorMessage = "Failed to parse contact list response"
                    };
                }

                // Step 4: Wait for export to be ready and get download URL
                string downloadUrl = await WaitForExportAndGetDownloadUrl(contactListId);
                if (string.IsNullOrEmpty(downloadUrl))
                {
                    return new ContactListProcessingResult
                    {
                        Status = ContactListProcessingStatus.SkippedTransientError,
                        ErrorMessage = "Export did not become available within timeout"
                    };
                }

                // Step 5: Download and process CSV
                int recordCount = await DownloadAndProcessCsv(contactListId, downloadUrl, targetTable);

                // Record successful completion
                RecordProcessingComplete(contactListId, recordCount);

                return new ContactListProcessingResult
                {
                    Status = ContactListProcessingStatus.Success,
                    RecordCount = recordCount
                };
            }
            catch (ContactListNotFoundException)
            {
                return new ContactListProcessingResult
                {
                    Status = ContactListProcessingStatus.SkippedPermanentError,
                    ErrorMessage = "Contact list not found (404) - likely deleted"
                };
            }
            catch (ContactListPermissionException)
            {
                return new ContactListProcessingResult
                {
                    Status = ContactListProcessingStatus.FailedCriticalError,
                    ErrorMessage = "Permission denied - indicates authentication/authorization issue"
                };
            }
            catch (ContactListMalformedResponseException ex)
            {
                return new ContactListProcessingResult
                {
                    Status = ContactListProcessingStatus.FailedCriticalError,
                    ErrorMessage = $"Malformed API response: {ex.Message}"
                };
            }
            catch (Exception ex)
            {
                return new ContactListProcessingResult
                {
                    Status = ContactListProcessingStatus.SkippedTransientError,
                    ErrorMessage = $"Unexpected error: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Simplified contact list export method based on the original working implementation
        /// This follows the pattern that was working before the refactoring
        /// </summary>
        private async Task<string> RequestContactListExportSimplified(string contactListId)
        {
            string exportUrl = $"{_baseUri}/api/v2/outbound/contactlists/{contactListId}/export";
            const int maxWaitSeconds = 90;

            try
            {
                // Step 1: POST to initiate the export (like the old implementation)
                _logger?.LogDebug("Initiating export for contact list {ContactListId} using simplified approach", contactListId);

                string postResponse = _jsonActions.JsonReturnString(exportUrl, _gcApiKey, "");

                // Use centralized error handling for POST response
                if (!string.IsNullOrEmpty(postResponse))
                {
                    try
                    {
                        var errorCheck = CheckForApiErrors(postResponse, contactListId);
                        if (errorCheck != null)
                        {
                            // Handle specific error types
                            if (errorCheck.Status == ContactListProcessingStatus.SkippedTransientError)
                            {
                                throw new Exception($"Transient error: {errorCheck.ErrorMessage}");
                            }
                            else
                            {
                                throw new ContactListNotFoundException($"Contact list {contactListId} export failed: {errorCheck.ErrorMessage}");
                            }
                        }
                    }
                    catch (ContactListNotFoundException)
                    {
                        throw; // Re-throw permanent errors
                    }
                    catch (ContactListPermissionException)
                    {
                        throw; // Re-throw permission errors
                    }
                    catch (Exception)
                    {
                        // If centralized error handling fails, fall back to basic checks
                        if (string.IsNullOrEmpty(postResponse) || postResponse.Length <= 20)
                        {
                            _logger?.LogWarning("Empty or short response when initiating export for contact list {ContactListId}", contactListId);
                            throw new ContactListNotFoundException($"Contact list {contactListId} export initiation failed");
                        }

                        // Check for basic error patterns
                        if (postResponse.Contains("\"error\"") || postResponse == "{}")
                        {
                            _logger?.LogWarning("Error response when initiating export for contact list {ContactListId}: {Response}",
                                contactListId, postResponse.Length > 200 ? postResponse.Substring(0, 200) + "..." : postResponse);
                            throw new ContactListNotFoundException($"Contact list {contactListId} export failed");
                        }
                    }
                }

                // Step 2: Wait a moment then start polling for the download URL
                await Task.Delay(1000);

                var startTime = DateTime.UtcNow;
                var timeout = TimeSpan.FromSeconds(maxWaitSeconds);

                while (DateTime.UtcNow - startTime < timeout)
                {
                    try
                    {
                        // GET the export status (like the old implementation)
                        string statusResponse = _jsonActions.JsonReturnString(exportUrl, _gcApiKey);

                        if (!string.IsNullOrEmpty(statusResponse) && statusResponse.Length > 20)
                        {
                            // Use centralized error handling for status response
                            try
                            {
                                var errorCheck = CheckForApiErrors(statusResponse, contactListId);
                                if (errorCheck != null)
                                {
                                    // Handle specific error types
                                    if (errorCheck.Status == ContactListProcessingStatus.SkippedTransientError)
                                    {
                                        _logger?.LogDebug("Transient error checking export status for contact list {ContactListId}, will retry: {ErrorMessage}",
                                            contactListId, errorCheck.ErrorMessage);
                                        await Task.Delay(2500);
                                        continue;
                                    }
                                    else
                                    {
                                        throw new ContactListNotFoundException($"Contact list {contactListId} export not available: {errorCheck.ErrorMessage}");
                                    }
                                }
                            }
                            catch (ContactListNotFoundException)
                            {
                                throw; // Re-throw permanent errors
                            }
                            catch (ContactListPermissionException)
                            {
                                throw; // Re-throw permission errors
                            }
                            catch (Exception)
                            {
                                // If centralized error handling fails, fall back to basic checks
                                if (statusResponse.Contains("\"error\""))
                                {
                                    _logger?.LogDebug("Error response when checking export status for contact list {ContactListId}, will retry: {Response}",
                                        contactListId, statusResponse.Length > 100 ? statusResponse.Substring(0, 100) + "..." : statusResponse);
                                    await Task.Delay(2500);
                                    continue;
                                }
                            }

                            try
                            {
                                // Parse the response to get the download URL (like the old implementation)
                                var contactListUrl = JsonConvert.DeserializeObject<ContactListUrl>(statusResponse);
                                if (!string.IsNullOrEmpty(contactListUrl?.uri))
                                {
                                    _logger?.LogInformation("✅ Export download URI ready for contact list {ContactListId}: {ExportUrl}",
                                        contactListId, contactListUrl.uri);
                                    return contactListUrl.uri;
                                }
                            }
                            catch (JsonException ex)
                            {
                                _logger?.LogDebug(ex, "Failed to parse export status response for contact list {ContactListId}", contactListId);
                            }
                        }

                        // Wait before next attempt (like the old implementation)
                        await Task.Delay(2500);
                    }
                    catch (ContactListNotFoundException)
                    {
                        throw; // Re-throw permanent errors
                    }
                    catch (ContactListPermissionException)
                    {
                        throw; // Re-throw permission errors
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogDebug(ex, "Error checking export status for contact list {ContactListId}, will retry", contactListId);
                        await Task.Delay(2500);
                    }
                }

                // Timeout reached
                _logger?.LogWarning("Timeout waiting for export URL for contact list {ContactListId} after {TimeoutSeconds} seconds",
                    contactListId, maxWaitSeconds);
                return string.Empty; // Return empty string for timeout instead of throwing exception
            }
            catch (ContactListNotFoundException)
            {
                throw; // Re-throw permanent errors
            }
            catch (ContactListPermissionException)
            {
                throw; // Re-throw permission errors
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Error in simplified export request for contact list {ContactListId}", contactListId);
                throw new ContactListNotFoundException($"Contact list {contactListId} export failed: {ex.Message}");
            }
        }

        private async Task<string> RequestContactListExport(string contactListId)
        {
            string exportUrl = $"{_baseUri}/api/v2/outbound/contactlists/{contactListId}/export";

            try
            {
                // Step 1: POST to initiate the export (empty body for contact list export)
                _logger?.LogDebug("Initiating export for contact list {ContactListId} via POST to {ExportUrl}", contactListId, exportUrl);

                // Use JsonReturnString for POST request (like the original implementation)
                string postResponse = _jsonActions.JsonReturnString(exportUrl, _gcApiKey, "");

                // Check for error responses in the POST response
                if (string.IsNullOrEmpty(postResponse) || postResponse.Length <= 20)
                {
                    _logger?.LogWarning("Empty or short response when initiating export for contact list {ContactListId}", contactListId);
                    throw new ContactListNotFoundException($"Contact list {contactListId} export initiation failed");
                }

                // Check for specific error patterns in the response
                if (postResponse.Contains("\"error\"") || postResponse == "{}")
                {
                    _logger?.LogWarning("Error response when initiating export for contact list {ContactListId}: {Response}",
                        contactListId, postResponse);
                    throw new ContactListNotFoundException($"Contact list {contactListId} export failed");
                }

                // Check for "export already in progress" error
                if (postResponse.Contains("contact.list.export.in.progress") ||
                    postResponse.Contains("Export already in progress"))
                {
                    _logger?.LogInformation("Export already in progress for contact list {ContactListId}, proceeding to check for download URI", contactListId);
                    // Continue to the GET step even if export is already in progress
                }
                else
                {
                    _logger?.LogDebug("Export initiated successfully for contact list {ContactListId}", contactListId);
                }

                // Step 2: Wait a moment for the export to be processed, then GET the download URI
                await Task.Delay(1000);
                return await WaitForExportAndGetDownloadUrl(contactListId);
            }
            catch (ContactListNotFoundException)
            {
                // Re-throw our custom exceptions
                throw;
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Error requesting export for contact list {ContactListId}", contactListId);
                throw;
            }
        }



        private ContactListProcessingResult? CheckForApiErrors(string response, string contactListId)
        {
            if (!GenesysErrorHandler.IsErrorResponse(response))
                return null;

            var errorResult = GenesysErrorHandler.CreateErrorResult(response);

            // Categorize errors
            switch (errorResult.ErrorCode?.ToLower())
            {
                case "not.found":
                case "contact.list.not.found":
                case "no.available.list.export.uri":
                    throw new ContactListNotFoundException($"Contact list {contactListId} not found or export not available");

                case "forbidden":
                case "unauthorized":
                    throw new ContactListPermissionException($"Permission denied for contact list {contactListId}");

                case "contact.list.export.in.progress":
                    // This is expected - the export request is being processed
                    return null;

                case "too.many.requests":
                case "rate.limit.exceeded":
                    return new ContactListProcessingResult
                    {
                        Status = ContactListProcessingStatus.SkippedTransientError,
                        ErrorMessage = $"Rate limit exceeded: {errorResult.ErrorMessage}"
                    };

                default:
                    // Check if this is an "export already in progress" error in the default case
                    if (errorResult.ErrorMessage?.Contains("Export already in progress") == true)
                    {
                        // This is an "export already in progress" error that wasn't caught by the error code
                        return null;
                    }

                    return new ContactListProcessingResult
                    {
                        Status = ContactListProcessingStatus.SkippedTransientError,
                        ErrorMessage = $"API error: {errorResult.ErrorMessage}"
                    };
            }
        }

        private ContactListObject? ParseContactListResponse(string response, string contactListId)
        {
            try
            {
                return JsonConvert.DeserializeObject<ContactListObject>(response, new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                });
            }
            catch (JsonException ex)
            {
                throw new ContactListMalformedResponseException($"Failed to parse contact list response for {contactListId}: {ex.Message}");
            }
        }

        private async Task<string> WaitForExportAndGetDownloadUrl(string contactListId)
        {
            var timeout = TimeSpan.FromSeconds(MAX_WAIT_FOR_EXPORT_SECONDS);
            var startTime = DateTime.UtcNow;

            while (DateTime.UtcNow - startTime < timeout)
            {
                try
                {
                    string exportStatusUrl = $"{_baseUri}/api/v2/outbound/contactlists/{contactListId}/export";

                    // Step 2: GET the export status to retrieve the download URI (like the original implementation)
                    _logger?.LogDebug("Getting export status for contact list {ContactListId} via GET to {ExportStatusUrl}", contactListId, exportStatusUrl);

                    string statusResponse = _jsonActions.JsonReturnString(exportStatusUrl, _gcApiKey);

                    // Check for error responses
                    if (string.IsNullOrEmpty(statusResponse) || statusResponse.Length <= 20)
                    {
                        _logger?.LogDebug("Empty or short response for contact list {ContactListId}, continuing to wait", contactListId);
                        await Task.Delay(2500);
                        continue;
                    }

                    // Check for specific error patterns
                    if (statusResponse.Contains("\"error\"") || statusResponse == "{}")
                    {
                        _logger?.LogDebug("Error response for contact list {ContactListId}, continuing to wait: {Response}",
                            contactListId, statusResponse.Length > 100 ? statusResponse.Substring(0, 100) + "..." : statusResponse);
                        await Task.Delay(2500);
                        continue;
                    }

                    // Check for specific error codes that indicate permanent failure
                    if (statusResponse.Contains("no.available.list.export.uri"))
                    {
                        _logger?.LogInformation("Contact list {ContactListId} export not available (no.available.list.export.uri) - skipping", contactListId);
                        throw new ContactListNotFoundException($"Contact list {contactListId} export not available");
                    }

                    if (statusResponse.Contains("not.found") || statusResponse.Contains("contact.list.not.found"))
                    {
                        _logger?.LogInformation("Contact list {ContactListId} not found - skipping", contactListId);
                        throw new ContactListNotFoundException($"Contact list {contactListId} not found");
                    }

                    try
                    {
                        // Parse the JSON response to extract the download URI
                        var contactListUrl = JsonConvert.DeserializeObject<ContactListUrl>(statusResponse);
                        if (!string.IsNullOrEmpty(contactListUrl?.uri))
                        {
                            _logger?.LogInformation("✅ Export download URI ready for contact list {ContactListId}: {ExportUrl}",
                                contactListId, contactListUrl.uri);
                            return contactListUrl.uri;
                        }
                        else
                        {
                            _logger?.LogDebug("Export response received for contact list {ContactListId} but no URI field found. Response: {ResponsePreview}",
                                contactListId, statusResponse.Length > 200 ? statusResponse.Substring(0, 200) + "..." : statusResponse);
                        }
                    }
                    catch (JsonException ex)
                    {
                        _logger?.LogDebug(ex, "Failed to parse export status response for contact list {ContactListId}. Response: {ResponsePreview}",
                            contactListId, statusResponse.Length > 200 ? statusResponse.Substring(0, 200) + "..." : statusResponse);
                    }

                    // No URL yet, continue waiting
                    _logger?.LogDebug("Export URL not ready yet for contact list {ContactListId}, continuing to wait", contactListId);
                }
                catch (ContactListNotFoundException)
                {
                    // Permanent error - contact list doesn't exist or export not available
                    throw; // Re-throw to be handled by calling method
                }
                catch (ContactListPermissionException)
                {
                    // Permanent error - permission denied
                    throw; // Re-throw to be handled by calling method
                }
                catch (Exception ex)
                {
                    // Transient error - log and retry
                    _logger?.LogDebug(ex, "Transient error checking export status for {ContactListId}, will retry", contactListId);
                }

                await Task.Delay(2500);
            }

            // Timeout reached - treat as transient error
            _logger?.LogWarning("Timeout waiting for export URL for contact list {ContactListId} after {TimeoutSeconds} seconds",
                contactListId, MAX_WAIT_FOR_EXPORT_SECONDS);
            return string.Empty;
        }

        private async Task<int> DownloadAndProcessCsv(string contactListId, string downloadUrl, DataTable targetTable)
        {
            using var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _gcApiKey);
            httpClient.Timeout = TimeSpan.FromMinutes(CSV_DOWNLOAD_TIMEOUT_MINUTES);

            using var response = await httpClient.GetAsync(downloadUrl);
            response.EnsureSuccessStatusCode();

            using var responseStream = await response.Content.ReadAsStreamAsync();
            using var reader = new StreamReader(responseStream, Encoding.UTF8);

            return await ProcessContactListCsvStream(contactListId, reader, targetTable);
        }

        /// <summary>
        /// Processes CSV stream and adds data to target table with proper schema handling
        /// Optimized for memory efficiency with streaming processing
        /// </summary>
        private async Task<int> ProcessContactListCsvStream(string contactListId, StreamReader reader, DataTable targetTable)
        {
            int recordCount = 0;
            const int BATCH_SIZE = 1000; // Process in batches to optimize memory usage

            try
            {
                string? headerLine = await reader.ReadLineAsync();
                if (string.IsNullOrEmpty(headerLine))
                {
                    _logger?.LogWarning("Empty CSV file for contact list {ContactListId}", contactListId);
                    return 0;
                }

                // Parse CSV headers
                string[] headers = ParseCsvLine(headerLine);
                if (headers.Length == 0)
                {
                    _logger?.LogWarning("No headers found in CSV for contact list {ContactListId}", contactListId);
                    return 0;
                }

                // Ensure required columns exist using centralized manager
                ContactListColumnManager.EnsureRequiredColumnsExist(targetTable);

                // For memory optimization, we need to analyze a sample of data first to determine column sizes
                // Then process the data in streaming batches
                var sampleData = await CollectSampleCsvData(reader, headers, Math.Min(BATCH_SIZE, 100));

                // Use centralized method to add columns with optimal sizing based on sample
                ContactListColumnManager.AddDynamicColumnsFromData(targetTable, headers, sampleData);

                // Process the sample data first
                recordCount += PopulateDataTableFromCsvData(targetTable, sampleData, contactListId, recordCount);

                // Now process the rest of the data in streaming batches
                recordCount += await ProcessRemainingCsvDataInBatches(reader, headers, targetTable, contactListId, recordCount, BATCH_SIZE);

                // Get the contact list name for better logging
                string contactListName = GetContactListNameById(contactListId);
                string listDisplayName = !string.IsNullOrEmpty(contactListName) ? $"{contactListName} ({contactListId})" : contactListId;

                _logger?.LogInformation("✅ CSV processing summary for {ListDisplayName}: {RecordCount} contacts processed, {HeaderCount} columns",
                    listDisplayName, recordCount, headers.Length);

                return recordCount;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing CSV stream for contact list {ContactListId}", contactListId);
                throw;
            }
        }

        /// <summary>
        /// Centralized column management for contact list data tables
        /// Ensures consistent column definitions between API and CSV processing
        ///
        /// KEYID STRATEGY:
        /// - API Processing: Uses actual Genesys contact ID (contact.id from API response)
        /// - CSV Processing: Uses generated key with "csv" identifier to distinguish from API contacts
        ///   Format: "{contactListId}_csv_{recordIndex}_{timestamp}"
        ///
        /// This ensures:
        /// 1. API contacts have their real Genesys IDs for potential future lookups
        /// 2. CSV contacts have deterministic, unique keys that won't conflict with API contacts
        /// 3. Both approaches avoid duplicate key violations
        /// </summary>
        private static class ContactListColumnManager
        {
            /// <summary>
            /// Required columns for odcontactlistdata table with their specifications
            /// </summary>
            public static readonly Dictionary<string, (Type Type, int? MaxLength, bool IsRequired)> RequiredColumns = new()
            {
                { "keyid", (typeof(string), 100, true) },
                { "contactlistid", (typeof(string), 50, true) },
                { "updated", (typeof(DateTime), null, true) }
            };

            /// <summary>
            /// Ensures all required columns exist in the target table with consistent specifications
            /// </summary>
            public static void EnsureRequiredColumnsExist(DataTable targetTable)
            {
                foreach (var kvp in RequiredColumns)
                {
                    string columnName = kvp.Key;
                    var (type, maxLength, isRequired) = kvp.Value;

                    if (!targetTable.Columns.Contains(columnName))
                    {
                        var column = targetTable.Columns.Add(columnName, type);
                        if (maxLength.HasValue && type == typeof(string))
                        {
                            column.MaxLength = maxLength.Value;
                        }
                        column.AllowDBNull = !isRequired;
                    }
                }
            }

            /// <summary>
            /// Adds dynamic columns with optimal sizing based on data analysis
            /// Consistent between API and CSV processing
            /// </summary>
            public static void AddDynamicColumnsFromData(DataTable targetTable, IEnumerable<string> headers,
                IEnumerable<Dictionary<string, string>> dataRows)
            {
                const int defaultDynamicColumnLength = 100;
                const int maxVarcharLength = 255;
                var columnMaxLengths = new Dictionary<string, int>();

                // Analyze all data to determine maximum lengths for each column
                foreach (var row in dataRows)
                {
                    foreach (var kvp in row)
                    {
                        string header = kvp.Key;
                        string value = kvp.Value ?? string.Empty;

                        if (!string.IsNullOrEmpty(header) && !string.IsNullOrEmpty(value))
                        {
                            int currentLength = value.Length;
                            if (!columnMaxLengths.ContainsKey(header) || columnMaxLengths[header] < currentLength)
                            {
                                columnMaxLengths[header] = currentLength;
                            }
                        }
                    }
                }

                // Add columns with appropriate sizing based on data analysis
                foreach (string header in headers)
                {
                    if (!string.IsNullOrEmpty(header) && !targetTable.Columns.Contains(header))
                    {
                        // Skip required columns as they're already handled
                        if (RequiredColumns.ContainsKey(header))
                            continue;

                        // Determine appropriate column length based on actual data content
                        int maxDataLength = columnMaxLengths.ContainsKey(header) ? columnMaxLengths[header] : 0;
                        bool useTextType = maxDataLength > maxVarcharLength || header.Length > maxVarcharLength;

                        if (useTextType)
                        {
                            targetTable.Columns.Add(header, typeof(string)); // TEXT field (no MaxLength set)
                        }
                        else
                        {
                            var column = targetTable.Columns.Add(header, typeof(string));
                            // Use a safe size that accommodates the data plus some buffer
                            int safeColumnLength = Math.Max(defaultDynamicColumnLength, Math.Min(maxDataLength + 50, maxVarcharLength));
                            column.MaxLength = safeColumnLength;
                        }
                    }
                }
            }

            /// <summary>
            /// Creates a new row with required fields populated consistently
            /// Handles keyid generation logic centrally for both API and CSV processing
            /// </summary>
            public static DataRow CreateRowWithRequiredFields(DataTable targetTable, string contactListId,
                string keyId = null, DateTime? updatedTime = null, ContactDataSource dataSource = ContactDataSource.Unknown,
                int recordIndex = 0, long baseTicks = 0)
            {
                var row = targetTable.NewRow();

                // Set required fields with consistent keyid logic
                row["keyid"] = GenerateKeyId(keyId, contactListId, dataSource, recordIndex, baseTicks);
                row["contactlistid"] = contactListId;
                row["updated"] = updatedTime ?? DateTime.UtcNow;

                return row;
            }

            /// <summary>
            /// Centralized keyid generation logic for consistent handling across all data sources
            /// </summary>
            public static string GenerateKeyId(string providedKeyId, string contactListId, ContactDataSource dataSource,
                int recordIndex = 0, long baseTicks = 0)
            {
                // If we have a provided keyid (from API), use it
                if (!string.IsNullOrEmpty(providedKeyId))
                {
                    return providedKeyId;
                }

                // Generate keyid based on data source
                return dataSource switch
                {
                    ContactDataSource.Api => Guid.NewGuid().ToString(), // Fallback for API when no contact ID available
                    ContactDataSource.Csv => $"{contactListId}_csv_{recordIndex}_{baseTicks}", // Deterministic for CSV
                    _ => Guid.NewGuid().ToString() // Default fallback
                };
            }

            /// <summary>
            /// Checks for duplicate keyids in the target table efficiently
            /// </summary>
            public static HashSet<string> GetExistingKeyIds(DataTable targetTable)
            {
                var existingKeys = new HashSet<string>();
                foreach (DataRow existingRow in targetTable.Rows)
                {
                    var existingKey = existingRow["keyid"]?.ToString();
                    if (!string.IsNullOrEmpty(existingKey))
                    {
                        existingKeys.Add(existingKey);
                    }
                }
                return existingKeys;
            }

            /// <summary>
            /// Populates dynamic data fields in a row with proper null handling
            /// </summary>
            public static void PopulateDynamicFields(DataRow row, Dictionary<string, string> data)
            {
                foreach (var kvp in data)
                {
                    string columnName = kvp.Key;
                    string value = kvp.Value;

                    if (row.Table.Columns.Contains(columnName))
                    {
                        // Handle null values properly for database compatibility
                        row[columnName] = string.IsNullOrEmpty(value) ? DBNull.Value : value;
                    }
                }
            }
        }

        /// <summary>
        /// Legacy method - now uses centralized column manager
        /// </summary>
        private void EnsureRequiredColumnsExist(DataTable targetTable, string contactListId)
        {
            ContactListColumnManager.EnsureRequiredColumnsExist(targetTable);
        }



        /// <summary>
        /// Parses a CSV line handling quoted fields and commas
        /// </summary>
        private string[] ParseCsvLine(string line)
        {
            var result = new List<string>();
            bool inQuotes = false;
            var currentField = new StringBuilder();

            for (int i = 0; i < line.Length; i++)
            {
                char c = line[i];

                if (c == '"')
                {
                    if (inQuotes && i + 1 < line.Length && line[i + 1] == '"')
                    {
                        // Escaped quote
                        currentField.Append('"');
                        i++; // Skip next quote
                    }
                    else
                    {
                        // Toggle quote state
                        inQuotes = !inQuotes;
                    }
                }
                else if (c == ',' && !inQuotes)
                {
                    // Field separator
                    result.Add(currentField.ToString());
                    currentField.Clear();
                }
                else
                {
                    currentField.Append(c);
                }
            }

            // Add the last field
            result.Add(currentField.ToString());

            return result.ToArray();
        }

        /// <summary>
        /// Collects a sample of CSV data for column analysis (memory optimized)
        /// </summary>
        private async Task<List<Dictionary<string, string>>> CollectSampleCsvData(StreamReader reader, string[] headers, int maxSampleSize)
        {
            var sampleData = new List<Dictionary<string, string>>();
            int sampleCount = 0;

            string? line;
            while ((line = await reader.ReadLineAsync()) != null && sampleCount < maxSampleSize)
            {
                if (string.IsNullOrWhiteSpace(line)) continue;

                string[] values = ParseCsvLine(line);
                var rowData = new Dictionary<string, string>();

                for (int i = 0; i < Math.Min(headers.Length, values.Length); i++)
                {
                    string header = headers[i];
                    string value = values[i];

                    if (!string.IsNullOrEmpty(header))
                    {
                        rowData[header] = value ?? string.Empty;
                    }
                }

                sampleData.Add(rowData);
                sampleCount++;
            }

            return sampleData;
        }

        /// <summary>
        /// Processes remaining CSV data in memory-efficient batches
        /// </summary>
        private async Task<int> ProcessRemainingCsvDataInBatches(StreamReader reader, string[] headers,
            DataTable targetTable, string contactListId, int startingRecordCount, int batchSize)
        {
            int totalRecordsProcessed = 0;
            var currentBatch = new List<Dictionary<string, string>>();

            string? line;
            while ((line = await reader.ReadLineAsync()) != null)
            {
                if (string.IsNullOrWhiteSpace(line)) continue;

                string[] values = ParseCsvLine(line);
                var rowData = new Dictionary<string, string>();

                for (int i = 0; i < Math.Min(headers.Length, values.Length); i++)
                {
                    string header = headers[i];
                    string value = values[i];

                    if (!string.IsNullOrEmpty(header))
                    {
                        rowData[header] = value ?? string.Empty;
                    }
                }

                currentBatch.Add(rowData);

                // Process batch when it reaches the batch size
                if (currentBatch.Count >= batchSize)
                {
                    int batchRecords = PopulateDataTableFromCsvData(targetTable, currentBatch, contactListId, startingRecordCount + totalRecordsProcessed);
                    totalRecordsProcessed += batchRecords;

                    // Clear the batch to free memory
                    currentBatch.Clear();

                    // Optional: Force garbage collection for very large datasets
                    if (totalRecordsProcessed % (batchSize * 10) == 0)
                    {
                        GC.Collect();
                        GC.WaitForPendingFinalizers();
                    }
                }
            }

            // Process any remaining records in the final batch
            if (currentBatch.Count > 0)
            {
                int finalBatchRecords = PopulateDataTableFromCsvData(targetTable, currentBatch, contactListId, startingRecordCount + totalRecordsProcessed);
                totalRecordsProcessed += finalBatchRecords;
                currentBatch.Clear();
            }

            return totalRecordsProcessed;
        }

        /// <summary>
        /// Legacy method - kept for backward compatibility but now calls the optimized sample method
        /// </summary>
        private async Task<List<Dictionary<string, string>>> CollectCsvData(StreamReader reader, string[] headers)
        {
            // For backward compatibility, collect all data (not memory optimized)
            // This method should only be used for small datasets
            return await CollectSampleCsvData(reader, headers, int.MaxValue);
        }

        /// <summary>
        /// Populates DataTable from CSV data with support for batch processing
        /// Uses centralized column management for consistency
        /// </summary>
        private int PopulateDataTableFromCsvData(DataTable targetTable, List<Dictionary<string, string>> tempData,
            string contactListId, int recordCountOffset = 0)
        {
            int recordCount = 0;
            int duplicateCount = 0;
            var currentTime = DateTime.UtcNow;
            long baseTicks = currentTime.Ticks;

            // Ensure required columns exist using centralized manager
            ContactListColumnManager.EnsureRequiredColumnsExist(targetTable);

            // Create HashSet to track existing keys for duplicate detection using centralized method
            var existingKeys = ContactListColumnManager.GetExistingKeyIds(targetTable);

            foreach (var rowData in tempData)
            {
                try
                {
                    // Generate consistent keyid for CSV contacts using centralized method
                    int totalRecordIndex = recordCountOffset + recordCount;
                    string keyId = ContactListColumnManager.GenerateKeyId(null, contactListId, ContactDataSource.Csv,
                        totalRecordIndex, baseTicks + recordCount);

                    // Check for duplicates (should be rare with our key generation strategy)
                    if (existingKeys.Contains(keyId))
                    {
                        duplicateCount++;
                        continue;
                    }

                    // Create row with required fields using centralized manager
                    var newRow = ContactListColumnManager.CreateRowWithRequiredFields(targetTable, contactListId, keyId, currentTime,
                        ContactDataSource.Csv, totalRecordIndex, baseTicks + recordCount);

                    // Populate dynamic fields using centralized manager
                    ContactListColumnManager.PopulateDynamicFields(newRow, rowData);

                    targetTable.Rows.Add(newRow);
                    existingKeys.Add(keyId); // Track this key to prevent duplicates within this batch
                    recordCount++;
                }
                catch (ConstraintException ex) when (ex.Message.Contains("is constrained to be unique"))
                {
                    // Handle duplicate key constraint as fallback
                    duplicateCount++;
                    _logger?.LogDebug("🔄 Duplicate key detected in CSV processing for contact list {ContactListId}: {ErrorMessage}", contactListId, ex.Message);
                    // Continue processing other records
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "❌ Error processing CSV row for contact list {ContactListId}", contactListId);
                    // Continue processing other records
                }
            }

            // Log summary if there were duplicates
            if (duplicateCount > 0)
            {
                _logger?.LogDebug("📋 CSV processing for contact list {ContactListId}: Skipped {DuplicateCount} duplicate records", contactListId, duplicateCount);
            }

            return recordCount;
        }
    }

    // Supporting classes and enums
    public enum ContactListProcessingStatus
    {
        Success,
        SkippedPermanentError,
        SkippedTransientError,
        FailedCriticalError
    }

    public enum ContactDataSource
    {
        Unknown,
        Api,
        Csv
    }

    public class ContactListProcessingResult
    {
        public ContactListProcessingStatus Status { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public int RecordCount { get; set; }
    }

    public class ContactListProcessingResults
    {
        public int SuccessCount { get; set; }
        public int SkippedCount { get; set; }
        public int FailedCount { get; set; }
        public int CriticalErrorCount { get; set; }
    }

    // Custom exceptions for better error categorization
    public class ContactListNotFoundException : Exception
    {
        public ContactListNotFoundException(string message) : base(message) { }
    }

    public class ContactListPermissionException : Exception
    {
        public ContactListPermissionException(string message) : base(message) { }
    }

    public class ContactListMalformedResponseException : Exception
    {
        public ContactListMalformedResponseException(string message) : base(message) { }
    }

    public class ContactListObject
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class ContactListUrl
    {
        public string uri { get; set; }
        public DateTime exportTimestamp { get; set; }
    }
}
