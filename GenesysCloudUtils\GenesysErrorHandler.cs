using System;
using System.Text.Json;
using Microsoft.Extensions.Logging;

namespace GenesysCloudUtils
{
    /// <summary>
    /// Centralized error handling utility for Genesys Cloud API responses
    /// Provides consistent error detection, message extraction, and user-friendly error reporting
    /// </summary>
    public static class GenesysErrorHandler
    {
        /// <summary>
        /// Determines if a response contains error indicators from Genesys Cloud
        /// </summary>
        /// <param name="response">The response to check (JSON or HTTP status)</param>
        /// <returns>True if the response contains error indicators</returns>
        public static bool IsErrorResponse(string response)
        {
            if (string.IsNullOrEmpty(response) || response == "{}")
                return true;

            // Check for HTTP error status responses (e.g., "HTTP NotFound", "HTTP 404", etc.)
            if (response.StartsWith("HTTP", StringComparison.OrdinalIgnoreCase))
            {
                // HTTP status responses are error responses
                return true;
            }

            // More specific error detection for Genesys Cloud JSON responses
            // Only flag as error if it contains explicit error indicators, not just any "code" or "message" field

            // Check for explicit error fields that indicate actual errors
            if (response.Contains("\"error\"") || response.Contains("\"error_description\""))
                return true;

            // Check for specific error patterns that indicate failures
            if (response.Contains("\"message\"") &&
                (response.Contains("not found") || response.Contains("forbidden") ||
                 response.Contains("unauthorized") || response.Contains("bad request") ||
                 response.Contains("internal server error") || response.Contains("service unavailable")))
                return true;

            // Check for specific error codes that indicate failures
            if (response.Contains("\"code\"") &&
                (response.Contains("not.found") || response.Contains("forbidden") ||
                 response.Contains("unauthorized") || response.Contains("bad.request") ||
                 response.Contains("internal.server.error") || response.Contains("service.unavailable") ||
                 response.Contains("no.available.list.export.uri")))
                return true;

            // Check for HTTP status codes that indicate errors
            if (response.Contains("\"statusCode\"") &&
                (response.Contains("400") || response.Contains("401") || response.Contains("403") ||
                 response.Contains("404") || response.Contains("500") || response.Contains("503")))
                return true;

            // If none of the above error patterns match, it's likely a successful response
            return false;
        }

        /// <summary>
        /// Extracts a user-friendly error message from a Genesys Cloud error response
        /// </summary>
        /// <param name="jsonResponse">The JSON error response</param>
        /// <returns>A user-friendly error message</returns>
        public static string ExtractErrorMessage(string jsonResponse)
        {
            if (string.IsNullOrEmpty(jsonResponse))
                return "Empty response";

            if (jsonResponse == "{}")
                return "Empty JSON response";

            try
            {
                // Extract JSON part if response includes HTTP status line prefix
                string jsonPart = ExtractJsonFromResponse(jsonResponse);

                // Try to parse as JSON and extract common error message fields
                var jsonDoc = JsonDocument.Parse(jsonPart);
                var root = jsonDoc.RootElement;

                // Check for various message fields in order of preference
                if (root.TryGetProperty("messageWithParams", out var messageWithParams) && messageWithParams.ValueKind == JsonValueKind.String)
                    return messageWithParams.GetString() ?? "Unknown error";

                if (root.TryGetProperty("message", out var message) && message.ValueKind == JsonValueKind.String)
                    return message.GetString() ?? "Unknown error";

                if (root.TryGetProperty("error", out var error))
                {
                    if (error.ValueKind == JsonValueKind.String)
                        return error.GetString() ?? "Unknown error";
                    
                    // Handle nested error objects
                    if (error.ValueKind == JsonValueKind.Object && error.TryGetProperty("message", out var nestedMessage))
                        return nestedMessage.GetString() ?? "Unknown error";
                }

                if (root.TryGetProperty("error_description", out var errorDesc) && errorDesc.ValueKind == JsonValueKind.String)
                    return errorDesc.GetString() ?? "Unknown error";

                return "Error response received";
            }
            catch
            {
                // If JSON parsing fails, try to extract meaningful text from the raw response
                return ExtractTextFromRawResponse(jsonResponse);
            }
        }

        /// <summary>
        /// Extracts JSON content from a response that may include HTTP status line prefix
        /// </summary>
        /// <param name="response">The response string</param>
        /// <returns>The JSON part of the response</returns>
        private static string ExtractJsonFromResponse(string response)
        {
            if (string.IsNullOrEmpty(response))
                return response;

            // Look for the first '{' character which indicates the start of JSON
            int jsonStart = response.IndexOf('{');
            if (jsonStart >= 0)
                return response.Substring(jsonStart);

            // If no JSON found, return the original response
            return response;
        }

        /// <summary>
        /// Extracts the error code from a Genesys Cloud error response
        /// </summary>
        /// <param name="jsonResponse">The JSON error response</param>
        /// <returns>The error code or "unknown" if not found</returns>
        public static string ExtractErrorCode(string jsonResponse)
        {
            if (string.IsNullOrEmpty(jsonResponse))
                return "empty_response";

            try
            {
                // Extract JSON part if response includes HTTP status line prefix
                string jsonPart = ExtractJsonFromResponse(jsonResponse);

                var jsonDoc = JsonDocument.Parse(jsonPart);
                var root = jsonDoc.RootElement;

                if (root.TryGetProperty("code", out var code) && code.ValueKind == JsonValueKind.String)
                    return code.GetString() ?? "unknown";

                if (root.TryGetProperty("statusCode", out var statusCode) && statusCode.ValueKind == JsonValueKind.String)
                    return statusCode.GetString() ?? "unknown";

                if (root.TryGetProperty("error", out var error))
                {
                    if (error.ValueKind == JsonValueKind.String)
                        return error.GetString() ?? "unknown";

                    // Handle nested error objects
                    if (error.ValueKind == JsonValueKind.Object && error.TryGetProperty("code", out var nestedCode))
                        return nestedCode.GetString() ?? "unknown";
                }

                if (root.TryGetProperty("status", out var status))
                    return $"http_{status}";

                return "unknown";
            }
            catch
            {
                return "parse_error";
            }
        }

        /// <summary>
        /// Creates a structured error result with user-friendly information
        /// </summary>
        /// <param name="response">The error response (JSON or HTTP status)</param>
        /// <returns>A structured error result</returns>
        public static GenesysErrorResult CreateErrorResult(string response)
        {
            // Handle HTTP status responses
            if (response.StartsWith("HTTP", StringComparison.OrdinalIgnoreCase))
            {
                return CreateHttpErrorResult(response);
            }

            // Handle JSON error responses
            return new GenesysErrorResult
            {
                IsError = true,
                ErrorMessage = ExtractErrorMessage(response),
                ErrorCode = ExtractErrorCode(response),
                RawResponse = response
            };
        }

        /// <summary>
        /// Creates an error result from an HTTP status response
        /// </summary>
        /// <param name="httpResponse">The HTTP status response</param>
        /// <returns>A structured error result</returns>
        private static GenesysErrorResult CreateHttpErrorResult(string httpResponse)
        {
            // Parse HTTP status responses like "HTTP NotFound" or "HTTP 404 Not Found"
            if (httpResponse.Contains("NotFound", StringComparison.OrdinalIgnoreCase) ||
                httpResponse.Contains("404"))
            {
                return new GenesysErrorResult
                {
                    IsError = true,
                    ErrorCode = "not.found",
                    ErrorMessage = "Resource not found",
                    RawResponse = httpResponse
                };
            }

            if (httpResponse.Contains("Unauthorized", StringComparison.OrdinalIgnoreCase) ||
                httpResponse.Contains("401"))
            {
                return new GenesysErrorResult
                {
                    IsError = true,
                    ErrorCode = "unauthorized",
                    ErrorMessage = "Unauthorized access",
                    RawResponse = httpResponse
                };
            }

            if (httpResponse.Contains("Forbidden", StringComparison.OrdinalIgnoreCase) ||
                httpResponse.Contains("403"))
            {
                return new GenesysErrorResult
                {
                    IsError = true,
                    ErrorCode = "forbidden",
                    ErrorMessage = "Access forbidden",
                    RawResponse = httpResponse
                };
            }

            // Default for other HTTP errors
            return new GenesysErrorResult
            {
                IsError = true,
                ErrorCode = "http.error",
                ErrorMessage = $"HTTP error: {httpResponse}",
                RawResponse = httpResponse
            };
        }

        /// <summary>
        /// Logs an error with consistent formatting and user-friendly messages
        /// </summary>
        /// <param name="logger">The logger instance</param>
        /// <param name="context">Context information (e.g., "Contact List Processing")</param>
        /// <param name="identifier">Identifier for the item being processed (e.g., contact list ID)</param>
        /// <param name="jsonResponse">The error response</param>
        /// <param name="logLevel">The log level to use (default: Warning)</param>
        public static void LogError(ILogger logger, string context, string identifier, string jsonResponse, LogLevel logLevel = LogLevel.Warning)
        {
            if (logger == null) return;

            var errorResult = CreateErrorResult(jsonResponse);
            
            switch (logLevel)
            {
                case LogLevel.Error:
                    logger.LogError("{Context}: Error processing {Identifier} - {ErrorMessage} (Code: {ErrorCode})",
                        context, identifier, errorResult.ErrorMessage, errorResult.ErrorCode);
                    break;
                case LogLevel.Warning:
                default:
                    logger.LogWarning("{Context}: Error processing {Identifier} - {ErrorMessage} (Code: {ErrorCode})",
                        context, identifier, errorResult.ErrorMessage, errorResult.ErrorCode);
                    break;
            }
        }

        /// <summary>
        /// Determines if an error should be retried based on the error code
        /// </summary>
        /// <param name="errorCode">The error code</param>
        /// <returns>True if the error is retryable</returns>
        public static bool IsRetryableError(string errorCode)
        {
            return errorCode switch
            {
                "contact.list.export.in.progress" => true,
                "http_429" => true, // Too Many Requests
                "http_503" => true, // Service Unavailable
                "http_504" => true, // Gateway Timeout
                "RequestTimeout" => true,
                _ => false
            };
        }

        /// <summary>
        /// Extracts meaningful text from raw response when JSON parsing fails
        /// </summary>
        /// <param name="rawResponse">The raw response text</param>
        /// <returns>Extracted meaningful text</returns>
        private static string ExtractTextFromRawResponse(string rawResponse)
        {
            if (string.IsNullOrEmpty(rawResponse))
                return "Invalid error response format";

            // Try to find common error patterns in non-JSON responses
            if (rawResponse.Contains("Access Forbidden"))
                return "Access forbidden - insufficient permissions";
            
            if (rawResponse.Contains("Not Found"))
                return "Resource not found";
            
            if (rawResponse.Contains("Bad Request"))
                return "Bad request - invalid parameters";
            
            if (rawResponse.Contains("Unauthorized"))
                return "Unauthorized - authentication required";

            // Return a truncated version of the response for logging
            return rawResponse.Length > 100 
                ? rawResponse.Substring(0, 100) + "..." 
                : rawResponse;
        }
    }

    /// <summary>
    /// Structured result for error information
    /// </summary>
    public class GenesysErrorResult
    {
        public bool IsError { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public string ErrorCode { get; set; } = string.Empty;
        public string RawResponse { get; set; } = string.Empty;
    }
}
