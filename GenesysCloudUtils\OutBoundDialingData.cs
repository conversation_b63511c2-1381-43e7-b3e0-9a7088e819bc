using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.WebSockets;
using System.Text;
using CSG.Common.ExtensionMethods;
using DBUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using StandardUtils;

namespace GenesysCloudUtils
{
    public class OutBoundDialingData
    {
        private readonly ILogger? _logger;

        public string CustomerKeyID { get; set; }
        public string G<PERSON>piKey { get; set; }
        public DataSet GCControlData { get; set; }
        private readonly Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        private readonly GCUtils GCUtilities;
        private readonly JsonUtils JsonActions;
        public string TimeZoneConfig { get; set; }

        // Constants
        private const string ININ_OUTBOUND_ID = "inin-outbound-id";
        private const string ININ_OUTBOUND_ID_LOWERCASE = "inin-outbound-id"; // For case-insensitive matching
        private const string ININ_OUTBOUND_ID_SNOWFLAKE = "inin_outbound_id"; // Snowflake uses underscores
        private const int CSV_PROCESSING_BATCH_SIZE = 1000; // Process CSV in small batches
        private const int DB_WRITE_BATCH_SIZE = 5000; // Write to DB in batches

        // Contact list export rate limiting constants
        // Dynamic timing approach: wait for full rate limit window (60s) from first API call in batch
        private const int CONTACT_LIST_EXPORT_BATCH_SIZE = 30; // Optimized batch size (20 operations per batch)
        private const int RATE_LIMIT_WINDOW_MS = 60000; // 60 second rate limit window

        private readonly DBUtils.DBUtils DBUtil;
        public string OAuthUser { get; set; }

        public OutBoundDialingData()
        {
            GCUtilities = new GCUtils();
            JsonActions = new JsonUtils();
            DBUtil = new DBUtils.DBUtils();
        }

        public OutBoundDialingData(ILogger? logger)
        {
            _logger = logger;
            GCUtilities = new GCUtils(logger);
            JsonActions = new JsonUtils(logger);
            DBUtil = new DBUtils.DBUtils();
        }

        public void Initialize()
        {
            GCUtilities.Initialize();

            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            _logger?.LogInformation("Obtaining API Key");
            GCApiKey = GCUtilities.GCApiKey;

            DBUtil.Initialize();
        }

        public async Task<DataTable> GetContactListsFromCCAsync()
        {
            // Initialize performance monitoring
            var performanceMetrics = new ContactListPerformanceMetrics
            {
                StartTime = DateTime.UtcNow,
                PeakMemoryUsageMB = GC.GetTotalMemory(false) / 1024 / 1024
            };

            int defaultDynamicColumnLength = 100;   // TODO: Move to an option
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            string JSONString = String.Empty;
            // maxWaitForListData: Max time to wait between requesting a contact list download and it being available.
            TimeSpan maxWaitForListData = TimeSpan.FromSeconds(90);
            // maxDownloadTimeout: Max time to wait for individual CSV download
            TimeSpan maxDownloadTimeout = TimeSpan.FromMinutes(5);
            Dictionary<string, string> listData = new();

            DataTable ContactLists = DBUtil.CreateInMemTable("odcontactlistdata");
            DataTable ContactListDetails = DBUtil.GetSQLTableData("select * from odcontactlistdetails", "odcontactlistdetails");

            // Process all contact lists regardless of modification date
            _logger?.LogInformation("Processing all contact lists regardless of modification date");

            // Skip download processing if no contact lists need to be processed
            if (ContactListDetails.Rows.Count == 0)
            {
                _logger?.LogInformation("ODContactLists: No contact lists to process, skipping download preparation");
                return ContactLists;
            }

            // Log the number of contact lists being processed for download
            _logger?.LogInformation("ODContactLists: Processing {ContactListCount} contact lists for download", ContactListDetails.Rows.Count);

            performanceMetrics.ContactListCount = ContactListDetails.Rows.Count;
            var apiTimer = System.Diagnostics.Stopwatch.StartNew();

            // Process contact list export requests in batches to respect rate limits
            await ProcessContactListExportRequestsInBatches(ContactListDetails, URI, listData);

            apiTimer.Stop();
            performanceMetrics.ApiRequestTime = apiTimer.Elapsed;

            var timer = System.Diagnostics.Stopwatch.StartNew();
            var csvProcessingTimer = System.Diagnostics.Stopwatch.StartNew();
            _logger?.LogInformation("Waiting up to {WaitTime} for list downloads to be available", maxWaitForListData);
            System.Threading.Thread.Sleep(1000);
            foreach (var contactListEntry in listData)
            {
                string contactListId = contactListEntry.Key;
                string jsonResponse = contactListEntry.Value;
                _logger?.LogInformation("Downloading List: {ContactListId}", contactListId);

                    // Check if the response is an error response or empty JSON
                    if (GenesysErrorHandler.IsErrorResponse(jsonResponse))
                    {
                        var errorResult = GenesysErrorHandler.CreateErrorResult(jsonResponse);

                        // Special handling for "export already in progress" error - continue processing as the retry loop will handle it
                        if (errorResult.ErrorCode == "contact.list.export.in.progress")
                        {
                            _logger?.LogWarning("Export already in progress for contact list {ContactListId}, will retry later: {ErrorMessage}",
                                contactListId, errorResult.ErrorMessage);
                            // Continue processing - the export request loop will handle the retry
                        }
                        else
                        {
                            GenesysErrorHandler.LogError(_logger, "Contact List Processing", contactListId, jsonResponse);

                            throw new InvalidOperationException($"Contact list {contactListId} failed with error: {errorResult.ErrorMessage}. " +
                                "Failing job to prevent data loss. Raw response: {jsonResponse}");
                        }
                    }

                    ContactListObject? contactListObj;
                    try
                    {
                        contactListObj = JsonConvert.DeserializeObject<ContactListObject>(
                            jsonResponse,
                            new JsonSerializerSettings
                            {
                                NullValueHandling = NullValueHandling.Ignore
                            });
                    }
                    catch (JsonException ex)
                    {
                        // FAIL FAST: JSON parsing errors indicate API returned unexpected response (likely HTML error page)
                        _logger?.LogError(ex, "Failed to parse contact list response for {ContactListId}. This indicates the API returned an unexpected response format (likely HTML error page instead of JSON). Response: {Response}",
                            contactListId, jsonResponse.Length > 500 ? jsonResponse.Substring(0, 500) + "..." : jsonResponse);

                        throw new InvalidOperationException($"Failed to parse contact list {contactListId} response. API returned unexpected format. " +
                            "This indicates a critical API issue that requires investigation.", ex);
                    }

                    if (contactListObj != null && contactListObj.id != null)
                        {
                            bool neededToRetry = false;
                            int retryCount = 0;
                            const int maxRetries = 10; // Limit retries to prevent infinite loops

                            do
                            {
                                try
                                {
                                    JSONString = JsonActions.JsonReturnString(URI + "/api/v2/outbound/contactlists/" + contactListId + "/export", GCApiKey);
                                    if (JSONString.Length > 20)
                                    {
                                        if (neededToRetry)
                                            _logger?.LogInformation("Contact list ID {ContactListId} ready in {Elapsed} after {RetryCount} retries",
                                                contactListId, timer.Elapsed, retryCount);
                                        else
                                            System.Threading.Thread.Sleep(150);

                                        break;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    _logger?.LogWarning(ex, "Error checking contact list {ContactListId} availability (attempt {RetryCount})",
                                        contactListId, retryCount + 1);
                                }

                                if (timer.Elapsed > maxWaitForListData)
                                {
                                    _logger?.LogWarning("Contact list ID {ContactListId} did not become available within {MaxWaitTime}, aborting after {RetryCount} retries",
                                        contactListId, maxWaitForListData, retryCount);
                                    break;
                                }

                                if (retryCount >= maxRetries)
                                {
                                    _logger?.LogWarning("Contact list ID {ContactListId} exceeded maximum retry attempts ({MaxRetries}), aborting",
                                        contactListId, maxRetries);
                                    break;
                                }

                                neededToRetry = true;
                                retryCount++;
                                System.Threading.Thread.Sleep(2500);
                            } while (true);
                            if (JSONString.Length > 20)
                            {
                                // Check if the response is an error response
                                if (GenesysErrorHandler.IsErrorResponse(JSONString))
                                {
                                    GenesysErrorHandler.LogError(_logger, "Contact List URL Processing", contactListId, JSONString);

                                    // FAIL FAST: Contact list URL processing errors indicate data loss
                                    var errorResult = GenesysErrorHandler.CreateErrorResult(JSONString);
                                    throw new InvalidOperationException($"Contact list URL processing failed for {contactListId}: {errorResult.ErrorMessage}. " +
                                        "Failing job to prevent data loss.");
                                }

                                try
                                {
                                    ContactListUrl? contactListUrl = JsonConvert.DeserializeObject<ContactListUrl>(JSONString,
                                      new JsonSerializerSettings
                                      {
                                          NullValueHandling = NullValueHandling.Ignore
                                      });

                                    //Now Get the CSV File.

                                    _logger?.LogDebug("Getting CSV file from: {Uri}", contactListUrl?.uri);
                                    if (contactListUrl?.uri != null)
                                    {
                                        try
                                        {
                                            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(contactListUrl.uri);
                                            request.Method = WebRequestMethods.Http.Get;
                                            request.Headers.Add("Authorization", "Bearer " + GCApiKey);
                                            request.AutomaticDecompression = DecompressionMethods.Deflate | DecompressionMethods.GZip;
                                            // Set timeout for CSV download
                                            request.Timeout = (int)maxDownloadTimeout.TotalMilliseconds;
                                            request.ReadWriteTimeout = (int)maxDownloadTimeout.TotalMilliseconds;

                                            using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
                                        {
                                            Stream responseStream = response.GetResponseStream();
                                            StreamReader readStream = null;
                                            if (response.CharacterSet == null)
                                            {
                                                readStream = new StreamReader(responseStream);
                                            }
                                            else
                                            {
                                                readStream = new StreamReader(responseStream,
                                                Encoding.GetEncoding(response.CharacterSet));
                                            }

                                            // Use streaming CSV processing to minimize memory usage
                                            var csvTimer = System.Diagnostics.Stopwatch.StartNew();

                                            // Process CSV using streaming approach
                                            DataTable contactListData = ProcessContactListStreaming(
                                                contactListId,
                                                responseStream,
                                                ContactLists,
                                                contactListId); // Use contactListId as name since we don't have access to the DataRow anymore

                                            csvTimer.Stop();

                                            // Add processed data to main table
                                            foreach (DataRow row in contactListData.Rows)
                                            {
                                                ContactLists.ImportRow(row);
                                            }

                                            // Clean up resources immediately
                                            response.Close();
                                            readStream.Close();
                                            contactListData.Dispose();

                                            _logger?.LogDebug("CSV processing completed for contact list {ContactListId} in {ElapsedMs}ms, {RowCount} rows added",
                                                contactListId, csvTimer.ElapsedMilliseconds, contactListData.Rows.Count);

                                            // Force garbage collection periodically for large contact lists
                                            if (ContactLists.Rows.Count % 50000 == 0)
                                            {
                                                GC.Collect();
                                                GC.WaitForPendingFinalizers();

                                                long currentMemoryMB = GC.GetTotalMemory(false) / 1024 / 1024;
                                                _logger?.LogDebug("Memory usage after processing {TotalRows} total rows: {MemoryUsageMB}MB",
                                                    ContactLists.Rows.Count, currentMemoryMB);
                                            }
                                        }
                                        }
                                        catch (WebException ex) when (ex.Status == WebExceptionStatus.Timeout)
                                        {
                                            _logger?.LogError(ex, "Timeout downloading CSV for contact list {ContactListId} after {Timeout} minutes",
                                                contactListId, maxDownloadTimeout.TotalMinutes);
                                            // Continue with the next contact list
                                            continue;
                                        }
                                        catch (WebException ex)
                                        {
                                            _logger?.LogError(ex, "Web exception downloading CSV for contact list {ContactListId}: {Status}",
                                                contactListId, ex.Status);
                                            // Continue with the next contact list
                                            continue;
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger?.LogError(ex, "Unexpected error downloading CSV for contact list {ContactListId}",
                                                contactListId);
                                            // Continue with the next contact list
                                            continue;
                                        }
                                    }
                                    else
                                    {
                                        _logger?.LogWarning("Contact list URL is null for {ContactListId}", contactListId);
                                    }
                                }
                                catch (Newtonsoft.Json.JsonException ex)
                                {
                                    _logger?.LogError(ex, "Failed to deserialize contact list URL for {ContactListId}: {Response}",
                                        contactListId, JSONString);

                                    // FAIL FAST: JSON parsing errors indicate API returned unexpected response
                                    throw new InvalidOperationException($"Failed to parse contact list URL response for {contactListId}. " +
                                        "API returned unexpected format, indicating a critical API issue.", ex);
                                }
                            }
                        }
                    }
                    catch (Newtonsoft.Json.JsonException ex)
                    {
                        _logger?.LogError(ex, "Failed to deserialize contact list {ContactListId} response: {Response}",
                            contactListId, jsonResponse);
                        // Continue with the next contact list
                        continue;
                    }
                }

            // Complete performance monitoring
            csvProcessingTimer.Stop();
            performanceMetrics.CsvProcessingTime = csvProcessingTimer.Elapsed;
            performanceMetrics.EndTime = DateTime.UtcNow;
            performanceMetrics.TotalRecords = ContactLists.Rows.Count;
            performanceMetrics.PeakMemoryUsageMB = Math.Max(performanceMetrics.PeakMemoryUsageMB, GC.GetTotalMemory(false) / 1024 / 1024);

            // Log comprehensive completion summary with performance metrics
            var totalTime = performanceMetrics.EndTime - performanceMetrics.StartTime;
            _logger?.LogInformation("ODContactLists: Completed processing {ProcessedCount} contact lists, retrieved {RecordCount} contact records in {TotalTime}",
                ContactListDetails.Rows.Count, ContactLists.Rows.Count, totalTime);

            _logger?.LogInformation("ODContactLists Performance: API={ApiTime}, CSV={CsvTime}, Peak Memory={PeakMemory}MB, Records/sec={RecordsPerSecond:F1}",
                performanceMetrics.ApiRequestTime,
                performanceMetrics.CsvProcessingTime,
                performanceMetrics.PeakMemoryUsageMB,
                performanceMetrics.TotalRecords / Math.Max(totalTime.TotalSeconds, 1));

            return ContactLists;
        }

        /// <summary>
        /// Synchronous wrapper for GetContactListsFromCCAsync for backward compatibility
        /// </summary>
        /// <returns>DataTable containing contact list data</returns>
        public DataTable GetContactListsFromCC()
        {
            return GetContactListsFromCCAsync().GetAwaiter().GetResult();
        }

        /// <summary>
        /// Processes contact list export requests in batches to respect rate limits
        /// </summary>
        /// <param name="contactListDetails">DataTable containing contact list details</param>
        /// <param name="baseUri">Base URI for API calls</param>
        /// <param name="listData">Dictionary to store successful export responses</param>
        private async Task ProcessContactListExportRequestsInBatches(DataTable contactListDetails, string baseUri, Dictionary<string, string> listData)
        {
            var contactListRows = contactListDetails.Rows.Cast<DataRow>().ToList();
            int totalContactLists = contactListRows.Count;
            int batchCount = (int)Math.Ceiling((double)totalContactLists / CONTACT_LIST_EXPORT_BATCH_SIZE);

            _logger?.LogInformation("Processing {TotalContactLists} contact list export requests in {BatchCount} batches of {BatchSize} (dynamic rate limiting: 60s window from first API call)",
                totalContactLists, batchCount, CONTACT_LIST_EXPORT_BATCH_SIZE);

            DateTime? firstApiCallTime = null;

            for (int batchIndex = 0; batchIndex < batchCount; batchIndex++)
            {
                var batchStartTime = DateTime.UtcNow;
                var batchContactLists = contactListRows
                    .Skip(batchIndex * CONTACT_LIST_EXPORT_BATCH_SIZE)
                    .Take(CONTACT_LIST_EXPORT_BATCH_SIZE)
                    .ToList();

                _logger?.LogInformation("Processing batch {BatchNumber}/{TotalBatches}: {BatchSize} contact lists (lists {StartIndex}-{EndIndex})",
                    batchIndex + 1, batchCount, batchContactLists.Count,
                    batchIndex * CONTACT_LIST_EXPORT_BATCH_SIZE + 1,
                    Math.Min((batchIndex + 1) * CONTACT_LIST_EXPORT_BATCH_SIZE, totalContactLists));

                // Process contact lists in this batch
                int successCount = 0;
                int errorCount = 0;

                foreach (var contactList in batchContactLists)
                {
                    string contactListId = contactList["id"].ToString();
                    try
                    {
                        // Record the time of the first API call for rate limit window tracking
                        if (firstApiCallTime == null)
                        {
                            firstApiCallTime = DateTime.UtcNow;
                            _logger?.LogDebug("Starting rate limit window timer at {FirstApiCallTime}", firstApiCallTime);
                        }

                        string exportUrl = $"{baseUri}/api/v2/outbound/contactlists/{contactListId}/export";
                        string jsonResponse = JsonActions.JsonReturnString(exportUrl, GCApiKey, "");

                        if (!string.IsNullOrEmpty(jsonResponse) && jsonResponse.Length > 20)
                        {
                            listData.Add(contactListId, jsonResponse);
                            successCount++;
                            _logger?.LogDebug("Successfully requested export for contact list {ContactListId}", contactListId);
                        }
                        else
                        {
                            errorCount++;
                            _logger?.LogWarning("Empty or invalid response for contact list {ContactListId}: {Response}",
                                contactListId, jsonResponse);
                        }
                    }
                    catch (Exception ex)
                    {
                        errorCount++;
                        _logger?.LogError(ex, "Error requesting contact list export for {ContactListId}", contactListId);
                    }

                    // Small delay between individual requests within batch to avoid overwhelming the API
                    await Task.Delay(100);
                }

                var batchDuration = DateTime.UtcNow - batchStartTime;
                _logger?.LogInformation("Completed batch {BatchNumber}/{TotalBatches} in {Duration}: {SuccessCount} successful, {ErrorCount} errors",
                    batchIndex + 1, batchCount, batchDuration, successCount, errorCount);

                // Wait between batches to respect rate limits (except for the last batch)
                if (batchIndex < batchCount - 1 && firstApiCallTime.HasValue)
                {
                    // Calculate how much time has elapsed since the first API call
                    var elapsedSinceFirstCall = DateTime.UtcNow - firstApiCallTime.Value;
                    var remainingWaitTime = TimeSpan.FromMilliseconds(RATE_LIMIT_WINDOW_MS) - elapsedSinceFirstCall;

                    if (remainingWaitTime > TimeSpan.Zero)
                    {
                        int waitSeconds = (int)Math.Ceiling(remainingWaitTime.TotalSeconds);
                        _logger?.LogInformation("Waiting {WaitSeconds}s to complete 60s rate limit window (elapsed: {ElapsedSeconds}s since first API call)",
                            waitSeconds, (int)elapsedSinceFirstCall.TotalSeconds);
                        await Task.Delay(remainingWaitTime);
                    }
                    else
                    {
                        _logger?.LogInformation("Rate limit window already elapsed ({ElapsedSeconds}s), proceeding immediately to next batch",
                            (int)elapsedSinceFirstCall.TotalSeconds);
                    }

                    // Reset the timer for the next batch
                    firstApiCallTime = null;
                }
            }

            _logger?.LogInformation("Completed all {BatchCount} batches for contact list export requests. Total successful: {SuccessfulCount}",
                batchCount, listData.Count);
        }

        /// <summary>
        /// Processes a single contact list CSV using streaming to minimize memory usage
        /// </summary>
        /// <param name="contactListId">Contact list identifier</param>
        /// <param name="csvStream">Stream containing CSV data</param>
        /// <param name="targetTable">Target DataTable structure</param>
        /// <param name="contactListName">Name of the contact list for logging</param>
        /// <returns>DataTable with processed contact list data</returns>
        private DataTable ProcessContactListStreaming(string contactListId, Stream csvStream, DataTable targetTable, string contactListName)
        {
            var processedTable = targetTable.Clone();
            var batchRows = new List<DataRow>();
            int rowCount = 0;
            int skippedRows = 0;

            try
            {
                using var reader = new StreamReader(csvStream);

                // Read header line
                string headerLine = reader.ReadLine();
                if (string.IsNullOrEmpty(headerLine))
                {
                    _logger?.LogWarning("Empty CSV file for contact list {ContactListId}", contactListId);
                    return processedTable;
                }

                // Parse headers using proper CSV parsing
                string[] headers = ParseCsvLine(headerLine);

                // Ensure all required columns exist in target table
                EnsureColumnsExist(processedTable, headers);

                // Stream process line by line
                string line;
                while ((line = reader.ReadLine()) != null)
                {
                    if (string.IsNullOrWhiteSpace(line)) continue;

                    // Parse CSV line using proper CSV parsing
                    string[] values = ParseCsvLine(line);

                    // Skip malformed rows
                    if (values.Length < headers.Length)
                    {
                        skippedRows++;
                        if (skippedRows <= 5) // Log first few skipped rows only
                        {
                            _logger?.LogWarning("Skipping malformed row {RowNumber} in contact list {ContactListId}: expected {ExpectedColumns} columns, got {ActualColumns}",
                                rowCount + 1, contactListId, headers.Length, values.Length);
                        }
                        continue;
                    }

                    // Create contact row
                    DataRow newRow = CreateContactRow(processedTable, contactListId, headers, values);
                    if (newRow != null)
                    {
                        batchRows.Add(newRow);
                        rowCount++;
                    }

                    // Process in batches to manage memory
                    if (batchRows.Count >= CSV_PROCESSING_BATCH_SIZE)
                    {
                        foreach (var row in batchRows)
                            processedTable.Rows.Add(row);

                        batchRows.Clear(); // Free memory immediately

                        // Log progress for large contact lists
                        if (rowCount % 10000 == 0)
                        {
                            _logger?.LogDebug("Processed {RowCount} rows for contact list {ContactListId} ({ContactListName})",
                                rowCount, contactListId, contactListName);
                        }
                    }
                }

                // Add remaining rows
                foreach (var row in batchRows)
                    processedTable.Rows.Add(row);

                _logger?.LogInformation("Completed streaming processing for contact list {ContactListId}: {RowCount} rows processed, {SkippedRows} rows skipped",
                    contactListId, rowCount, skippedRows);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error during streaming CSV processing for contact list {ContactListId}", contactListId);
                throw;
            }

            return processedTable;
        }

        /// <summary>
        /// Ensures all CSV columns exist in the target DataTable
        /// </summary>
        private void EnsureColumnsExist(DataTable targetTable, string[] headers)
        {
            int defaultDynamicColumnLength = 100; // TODO: Move to configuration

            foreach (string header in headers)
            {
                string columnName = header.ToLower();
                if (!targetTable.Columns.Contains(columnName))
                {
                    DataColumn newCol = new(columnName, typeof(string));
                    newCol.MaxLength = defaultDynamicColumnLength;
                    targetTable.Columns.Add(newCol);
                }
            }
            targetTable.AcceptChanges();
        }

        /// <summary>
        /// Creates a contact row from CSV data
        /// </summary>
        private DataRow CreateContactRow(DataTable targetTable, string contactListId, string[] headers, string[] values)
        {
            try
            {
                // Find the ININ_OUTBOUND_ID column index (case-insensitive search)
                int outboundIdIndex = FindOutboundIdIndex(headers);
                if (outboundIdIndex == -1)
                {
                    _logger?.LogWarning("Required column {RequiredColumn} not found in contact list {ContactListId}. Available columns: {AvailableColumns}",
                        ININ_OUTBOUND_ID, contactListId, string.Join(", ", headers));
                    return null;
                }

                string outboundId = outboundIdIndex < values.Length ? values[outboundIdIndex] : "";
                string keyId = contactListId + "|" + outboundId;
                DataRow newRow = targetTable.NewRow();

                // Use safe assignment to handle null values
                newRow["keyid"] = keyId;
                newRow["contactlistid"] = contactListId;

                // Set the outbound ID using the correct column name for the database type
                string outboundColumnName = GetOutboundIdColumnName(targetTable);
                newRow[outboundColumnName] = !string.IsNullOrEmpty(outboundId) ? (object)outboundId : DBNull.Value;
                newRow["updated"] = DateTime.UtcNow;

                // Populate other columns with proper null handling
                string outboundColumnNameLower = outboundColumnName.ToLower();
                for (int i = 0; i < headers.Length && i < values.Length; i++)
                {
                    string columnName = headers[i].ToLower();

                    if (columnName != "keyid" && columnName != "updated" && columnName != outboundColumnNameLower)
                    {
                        var column = targetTable.Columns[columnName];
                        if (column != null)
                        {
                            string val = values[i];

                            // Handle null or empty values
                            if (string.IsNullOrEmpty(val))
                            {
                                newRow[columnName] = DBNull.Value;
                            }
                            else if (column.DataType == typeof(string))
                            {
                                var colLength = column.MaxLength > 0 ? column.MaxLength : 100;

                                if (val.Length > colLength)
                                {
                                    _logger?.LogWarning("Truncating column {ColumnName} from {Length} to {MaxLength} characters for contact {KeyId}",
                                        columnName, val.Length, colLength, keyId);
                                    val = val.Substring(0, colLength);
                                }
                                newRow[columnName] = val;
                            }
                            else
                            {
                                // For non-string columns, attempt to convert or set to DBNull
                                newRow[columnName] = !string.IsNullOrEmpty(val) ? (object)val : DBNull.Value;
                            }
                        }
                    }
                }

                return newRow;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error creating contact row for contact list {ContactListId}", contactListId);
                return null;
            }
        }

        /// <summary>
        /// Finds the index of the outbound ID column in headers (case-insensitive)
        /// </summary>
        private int FindOutboundIdIndex(string[] headers)
        {
            // Try exact match first
            int index = Array.IndexOf(headers, ININ_OUTBOUND_ID);
            if (index != -1) return index;

            // Try case-insensitive match
            for (int i = 0; i < headers.Length; i++)
            {
                if (string.Equals(headers[i], ININ_OUTBOUND_ID, StringComparison.OrdinalIgnoreCase))
                {
                    return i;
                }
            }

            return -1;
        }

        /// <summary>
        /// Gets the correct outbound ID column name based on database type
        /// </summary>
        private string GetOutboundIdColumnName(DataTable targetTable)
        {
            // Check if the table has the column with quotes (PostgreSQL)
            if (targetTable.Columns.Contains("inin-outbound-id"))
                return "inin-outbound-id";

            // Check for Snowflake format (underscores)
            if (targetTable.Columns.Contains("inin_outbound_id"))
                return "inin_outbound_id";

            // Default to standard format
            return ININ_OUTBOUND_ID;
        }

        /// <summary>
        /// Parses a CSV line properly handling quoted fields with commas
        /// </summary>
        private string[] ParseCsvLine(string line)
        {
            if (string.IsNullOrEmpty(line))
                return new string[0];

            var result = new List<string>();
            bool inQuotes = false;
            var currentField = new StringBuilder();

            for (int i = 0; i < line.Length; i++)
            {
                char c = line[i];

                if (c == '"')
                {
                    if (inQuotes && i + 1 < line.Length && line[i + 1] == '"')
                    {
                        // Escaped quote
                        currentField.Append('"');
                        i++; // Skip next quote
                    }
                    else
                    {
                        // Toggle quote state
                        inQuotes = !inQuotes;
                    }
                }
                else if (c == ',' && !inQuotes)
                {
                    // Field separator
                    result.Add(currentField.ToString());
                    currentField.Clear();
                }
                else
                {
                    currentField.Append(c);
                }
            }

            // Add the last field
            result.Add(currentField.ToString());

            return result.ToArray();
        }


    }

    /// <summary>
    /// Performance metrics for contact list processing
    /// </summary>
    public class ContactListPerformanceMetrics
    {
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public int ContactListCount { get; set; }
        public int TotalRecords { get; set; }
        public long PeakMemoryUsageMB { get; set; }
        public TimeSpan ApiRequestTime { get; set; }
        public TimeSpan CsvProcessingTime { get; set; }
        public TimeSpan DatabaseWriteTime { get; set; }
        public Dictionary<string, TimeSpan> ContactListTimes { get; set; } = new();
    }

    public class ContactListObject
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class ContactListUrl
    {
        public string uri { get; set; }
        public DateTime exportTimestamp { get; set; }
    }
}
// spell-checker: ignore: contactlistid
